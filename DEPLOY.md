# 🚀 Guía de Deploy - Sabiedumbre

## 📋 Resumen
- **Frontend**: Netlify (SPA con Vue 3 + <PERSON>uasar)
- **Backend**: Railway (Node.js + Express + MongoDB)
- **Base de Datos**: MongoDB Atlas (ya configurado)

## 🌐 **Deploy del Backend en Railway**

### 1. Crear cuenta en Railway
1. Ve a [Railway.app](https://railway.app)
2. Regístrate con GitHub
3. Conecta tu repositorio

### 2. Configurar el proyecto
1. **New Project** → **Deploy from GitHub repo**
2. Selecciona `sabiedumbre`
3. **Root Directory**: `backend`
4. **Start Command**: `npm start`

### 3. Variables de entorno en Railway
```env
NODE_ENV=production
PORT=3001
MONGODB_URI=mongodb+srv://sabiedumbre-user:<EMAIL>/sabiedumbre?retryWrites=true&w=majority
FRONTEND_URL=https://tu-app.netlify.app
JWT_SECRET=tu-clave-secreta-super-segura-aqui
BCRYPT_ROUNDS=12
```

### 4. Deploy automático
- Railway detectará automáticamente Node.js
- Instalará dependencias con `npm install`
- Iniciará con `npm start`
- URL generada: `https://tu-proyecto.railway.app`

## 🎨 **Deploy del Frontend en Netlify**

### 1. Crear cuenta en Netlify
1. Ve a [Netlify.com](https://netlify.com)
2. Regístrate con GitHub
3. **Add new site** → **Import an existing project**

### 2. Configurar el build
```
Build command: npm run build
Publish directory: dist/spa
```

### 3. Variables de entorno en Netlify
```env
VITE_API_URL=https://tu-backend.railway.app/api
```

### 4. Configuración automática
- Netlify detectará Vue.js automáticamente
- Usará el archivo `netlify.toml` para configuración
- Redirects configurados para SPA
- Headers de seguridad incluidos

## 🔄 **Proceso Completo**

### Paso 1: Subir código a GitHub
```bash
git add .
git commit -m "Deploy ready: Frontend + Backend"
git push origin main
```

### Paso 2: Deploy Backend (Railway)
1. Conectar repo en Railway
2. Configurar variables de entorno
3. Deploy automático
4. Copiar URL generada

### Paso 3: Deploy Frontend (Netlify)
1. Conectar repo en Netlify
2. Configurar `VITE_API_URL` con URL de Railway
3. Deploy automático
4. Copiar URL generada

### Paso 4: Actualizar CORS
1. En Railway, actualizar `FRONTEND_URL` con URL de Netlify
2. Redeploy automático

## ✅ **Verificación**

### Backend funcionando:
- `https://tu-backend.railway.app/api/health` → Status OK
- `https://tu-backend.railway.app/api/figures` → Lista de figuras
- `https://tu-backend.railway.app/api/ideas` → Lista de ideas

### Frontend funcionando:
- `https://tu-app.netlify.app` → Aplicación cargando
- Crear prompts → Se guardan en MongoDB
- Modo oscuro → Persiste en refresh

## 🔧 **Troubleshooting**

### Error CORS:
- Verificar `FRONTEND_URL` en Railway
- Verificar `VITE_API_URL` en Netlify

### Error 404 en rutas:
- Verificar `netlify.toml` está en la raíz
- Redirects configurados correctamente

### Error de conexión a MongoDB:
- Verificar `MONGODB_URI` en Railway
- Verificar IP whitelist en MongoDB Atlas (0.0.0.0/0)

### Build fallido:
- Verificar versión de Node.js (18+)
- Verificar dependencias en package.json

## 🎯 **URLs Finales**
- **Frontend**: `https://sabiedumbre.netlify.app`
- **Backend**: `https://sabiedumbre-api.railway.app`
- **API Health**: `https://sabiedumbre-api.railway.app/api/health`

## 🚀 **Deploy Automático**
Una vez configurado:
- Cada push a `main` → Deploy automático en ambos servicios
- Netlify: Frontend actualizado
- Railway: Backend actualizado
- Zero downtime deployments

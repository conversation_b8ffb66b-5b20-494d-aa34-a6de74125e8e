# 🧠 Sabiedumbre

**Genera textos creativos mezclando figuras del pensamiento con conceptos profundos**

Sabiedumbre es una aplicación interactiva que permite crear prompts únicos combinando perspectivas de filósofos, artistas, científicos y pensadores históricos con conceptos fundamentales como libertad, belleza, tiempo, identidad y más.

## ✨ Características

- **🎭 Figuras Históricas**: Combina perspectivas de <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> y más
- **💡 Conceptos Profundos**: Explora ideas como libertad, inconsciente, belleza, tiempo, identidad, amor, muerte
- **📝 Múltiples Estilos**: Genera ensayos filosóficos, diálogos imaginarios, narrativas cortas o poemas líricos
- **🔍 Exploración Visual**: Navega y selecciona contenido de forma intuitiva
- **💾 Historial**: Guarda y reutiliza tus prompts favoritos
- **🌙 Modo Oscuro**: Interfaz adaptable para cualquier momento del día

## 🚀 Inicio Rápido

### Instalar dependencias

```bash
npm install
# o
yarn install
```

### Ejecutar en modo desarrollo

```bash
npm run dev
# o
quasar dev
```

La aplicación estará disponible en `http://localhost:9000` (o el puerto disponible más cercano).

### Construir para producción

```bash
npm run build
# o
quasar build
```

## 🏗️ Tecnologías

- **Frontend**: Vue 3 + Quasar Framework
- **Datos**: JSON local (transición futura a MongoDB/Supabase)
- **Estilos**: Quasar Components + CSS personalizado
- **Routing**: Vue Router
- **Estado**: Composition API + localStorage

## 📁 Estructura del Proyecto

```
src/
├── components/
│   ├── ModalMezcla.vue      # Modal principal para crear mezclas
│   └── IdeaSelect.vue       # Componente para selección visual
├── pages/
│   ├── IndexPage.vue        # Página de bienvenida
│   ├── MezclaPage.vue       # Página principal de creación
│   └── ExplorePage.vue      # Exploración visual de contenido
├── data/
│   ├── figures.json         # Base de datos de figuras
│   └── ideas.json           # Base de datos de ideas
├── composables/
│   └── useGeneratePrompt.js # Lógica de generación de prompts
└── layouts/
    └── MainLayout.vue       # Layout principal con navegación
```

## 🎯 Cómo Usar

1. **Explorar**: Navega por las figuras e ideas disponibles
2. **Seleccionar**: Elige hasta 2 figuras y 2 conceptos
3. **Combinar**: El sistema genera automáticamente un prompt creativo
4. **Personalizar**: Selecciona el estilo (ensayo, diálogo, narrativa, poema)
5. **Copiar**: Usa el prompt en tu herramienta de IA favorita (ChatGPT, DeepSeek, etc.)

## 🔮 Ejemplos de Mezclas

**Nietzsche + Frida Kahlo × Dolor**

> "Escribe un ensayo que combine las perspectivas de Nietzsche y Frida Kahlo sobre el concepto del dolor como transformación creativa..."

**Einstein × Tiempo + Belleza**

> "Crea un diálogo imaginario donde Einstein reflexiona sobre la relación entre el tiempo y la belleza en el universo..."

## 🛠️ Desarrollo

### Comandos útiles

```bash
# Linting
npm run lint

# Formateo de código
npm run format

# Modo desarrollo con hot-reload
npm run dev
```

### Agregar nuevas figuras o ideas

Edita los archivos JSON en `src/data/`:

**figures.json**:

```json
{
  "id": "fig_new",
  "name": "Nombre de la Figura",
  "category": "filosofía|arte|ciencia|literatura|psicología",
  "tags": ["tag1", "tag2", "tag3"],
  "description": "Descripción breve de la figura"
}
```

**ideas.json**:

```json
{
  "id": "idea_new",
  "name": "nombre del concepto",
  "domain": "filosofía|psicología|estética|ética|política|epistemología",
  "tags": ["tag1", "tag2"],
  "description": "Descripción del concepto"
}
```

## 🗄️ Base de Datos MongoDB

La aplicación ahora usa MongoDB para almacenar datos:

### Configuración del Backend

```bash
cd backend
npm install
cp .env.example .env
# Configurar MONGODB_URI en .env con tu connection string de Atlas
npm run seed    # Poblar base de datos con 50 figuras y 50 ideas
npm run dev     # Iniciar servidor API en puerto 3001
```

### Estructura de Datos Simplificada

- **Figuras**: `{ id: 1, name: "Nietzsche", category: "filosofía" }`
- **Ideas**: `{ id: 1, name: "libertad", domain: "filosofía" }`
- **Prompts**: Texto generado + figuras/ideas usadas + estilo + timestamp

### Nuevas Características

- ✅ **Persistencia real** en MongoDB Atlas (gratis)
- ✅ **API RESTful** con Express + Mongoose
- ✅ **Estados de loading** mientras cargan datos
- ✅ **Fallback local** si la API no está disponible
- ✅ **Búsqueda y filtrado** optimizados con índices
- ✅ **Modal mejorado** con altura máxima 100vh y botón X a la derecha

## 🚧 Roadmap

- [x] **Fase 1**: ✅ MVP con JSON local
- [x] **Fase 2**: ✅ Base de datos en la nube (MongoDB Atlas)
- [x] **Fase 3**: ✅ API RESTful con backend
- [ ] **Fase 4**: Integración directa con APIs de IA
- [ ] **Fase 5**: Sistema de usuarios y favoritos
- [ ] **Fase 6**: Generación de imágenes complementarias
- [ ] **Fase 7**: Comunidad y compartir creaciones

## 🤝 Contribuir

¡Las contribuciones son bienvenidas! Por favor:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 👨‍💻 Autor

**M. Martín** - [bv-bjj](https://github.com/bv-bjj)

---

_"La sabiduría no es producto de la escolarización, sino del intento de toda la vida de adquirirla." - Albert Einstein_

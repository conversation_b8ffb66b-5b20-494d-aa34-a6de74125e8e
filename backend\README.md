# Sabiedumbre Backend API

Backend API para la aplicación Sabiedumbre, construido con Node.js, Express y MongoDB.

## 🚀 Instalación y Configuración

### Prerrequisitos

- Node.js (v16 o superior)
- MongoDB (local o MongoDB Atlas)
- npm o yarn

### Instalación

1. **Instalar dependencias:**
```bash
cd backend
npm install
```

2. **Configurar variables de entorno:**
```bash
cp .env.example .env
```

Edita el archivo `.env` con tus configuraciones:
```env
PORT=3001
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/sabiedumbre
FRONTEND_URL=http://localhost:9000
```

3. **Iniciar MongoDB:**
```bash
# Si usas MongoDB local
mongod

# O usar MongoDB Atlas (configurar MONGODB_URI en .env)
```

4. **Poblar la base de datos:**
```bash
npm run seed
```

5. **Iniciar el servidor:**
```bash
# Desarrollo (con nodemon)
npm run dev

# Producción
npm start
```

## 📊 API Endpoints

### Health Check
- `GET /api/health` - Verificar estado del servidor

### Figuras
- `GET /api/figures` - Obtener todas las figuras
- `GET /api/figures/:id` - Obtener figura por ID
- `GET /api/figures/meta/categories` - Obtener categorías
- `POST /api/figures` - Crear nueva figura
- `PUT /api/figures/:id` - Actualizar figura
- `DELETE /api/figures/:id` - Eliminar figura

### Ideas
- `GET /api/ideas` - Obtener todas las ideas
- `GET /api/ideas/:id` - Obtener idea por ID
- `GET /api/ideas/meta/domains` - Obtener dominios
- `POST /api/ideas` - Crear nueva idea
- `PUT /api/ideas/:id` - Actualizar idea
- `DELETE /api/ideas/:id` - Eliminar idea

### Prompts
- `GET /api/prompts` - Obtener prompts del usuario
- `GET /api/prompts/:id` - Obtener prompt por ID
- `POST /api/prompts` - Crear nuevo prompt
- `DELETE /api/prompts/:id` - Eliminar prompt
- `GET /api/prompts/stats/:userId` - Obtener estadísticas del usuario

## 🗄️ Estructura de Datos

### Figure
```json
{
  "id": 1,
  "name": "Nietzsche",
  "category": "filosofía"
}
```

### Idea
```json
{
  "id": 1,
  "name": "libertad",
  "domain": "filosofía"
}
```

### Prompt
```json
{
  "prompt": "Texto del prompt generado...",
  "figures": [
    {
      "id": 1,
      "name": "Nietzsche",
      "category": "filosofía"
    }
  ],
  "ideas": [
    {
      "id": 1,
      "name": "libertad",
      "domain": "filosofía"
    }
  ],
  "style": "ensayo",
  "userId": "anonymous"
}
```

## 🔧 Scripts Disponibles

- `npm start` - Iniciar servidor en producción
- `npm run dev` - Iniciar servidor en desarrollo con nodemon
- `npm run seed` - Poblar base de datos con datos iniciales

## 📝 Notas

- El servidor corre por defecto en el puerto 3001
- La base de datos se llama `sabiedumbre`
- Los IDs son números secuenciales (1, 2, 3...)
- Se eliminaron los campos `tags` y `description` para simplificar
- CORS está configurado para permitir el frontend en localhost:9000

## 🚀 Despliegue

Para producción, asegúrate de:

1. Configurar `NODE_ENV=production`
2. Usar una base de datos MongoDB segura
3. Configurar las variables de entorno apropiadas
4. Usar un proceso manager como PM2

```bash
npm install -g pm2
pm2 start server.js --name sabiedumbre-api
```

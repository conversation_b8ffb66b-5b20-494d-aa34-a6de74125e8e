const mongoose = require('mongoose');

const figureSchema = new mongoose.Schema({
  id: {
    type: Number,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['filosofía', 'arte', 'ciencia', 'literatura', 'psicología', 'música', 'política', 'historia'],
    lowercase: true
  }
}, {
  timestamps: true,
  collection: 'figures'
});

// Indexes for better performance
figureSchema.index({ name: 'text' });
figureSchema.index({ category: 1 });
figureSchema.index({ id: 1 });

module.exports = mongoose.model('Figure', figureSchema);

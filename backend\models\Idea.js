const mongoose = require("mongoose");

const ideaSchema = new mongoose.Schema(
  {
    id: {
      type: Number,
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    domain: {
      type: String,
      required: true,
      enum: [
        "filosofía",
        "psicología",
        "estética",
        "ética",
        "política",
        "epistemología",
        "metafísica",
        "lógica",
      ],
      lowercase: true,
    },
  },
  {
    timestamps: true,
    collection: "ideas",
  }
);

// Indexes for better performance
ideaSchema.index({ name: "text" });
ideaSchema.index({ domain: 1 });

module.exports = mongoose.model("Idea", ideaSchema);

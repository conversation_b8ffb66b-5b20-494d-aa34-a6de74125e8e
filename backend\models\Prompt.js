const mongoose = require('mongoose');

const promptSchema = new mongoose.Schema({
  prompt: {
    type: String,
    required: true
  },
  figures: [{
    id: Number,
    name: String,
    category: String
  }],
  ideas: [{
    id: Number,
    name: String,
    domain: String
  }],
  style: {
    type: String,
    required: true,
    enum: ['ensayo', 'dialogo', 'narrativa', 'poema']
  },
  userId: {
    type: String,
    default: 'anonymous' // Para futuras implementaciones de usuarios
  }
}, {
  timestamps: true,
  collection: 'prompts'
});

// Indexes
promptSchema.index({ userId: 1, createdAt: -1 });
promptSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Prompt', promptSchema);

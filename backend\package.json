{"name": "sabiedumbre-backend", "version": "1.0.0", "description": "Backend API for Sabiedumbre application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedDatabase.js", "build": "echo 'No build step required'", "postinstall": "echo 'Backend ready for deployment'"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["sabiedumbre", "api", "mongodb", "express"], "author": "Sabiedumbre Team", "license": "MIT"}
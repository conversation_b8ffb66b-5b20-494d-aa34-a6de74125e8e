const express = require('express');
const router = express.Router();
const Figure = require('../models/Figure');

// GET /api/figures - Get all figures
router.get('/', async (req, res) => {
  try {
    const { category, search, limit = 100, page = 1 } = req.query;
    
    let query = {};
    
    // Filter by category
    if (category) {
      query.category = category.toLowerCase();
    }
    
    // Search by name
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }
    
    const skip = (page - 1) * limit;
    
    const figures = await Figure.find(query)
      .sort({ id: 1 })
      .limit(parseInt(limit))
      .skip(skip)
      .lean();
    
    const total = await Figure.countDocuments(query);
    
    res.json({
      figures,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching figures:', error);
    res.status(500).json({ error: 'Error fetching figures' });
  }
});

// GET /api/figures/:id - Get figure by ID
router.get('/:id', async (req, res) => {
  try {
    const figure = await Figure.findOne({ id: parseInt(req.params.id) }).lean();
    
    if (!figure) {
      return res.status(404).json({ error: 'Figure not found' });
    }
    
    res.json(figure);
  } catch (error) {
    console.error('Error fetching figure:', error);
    res.status(500).json({ error: 'Error fetching figure' });
  }
});

// GET /api/figures/categories - Get all categories
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = await Figure.distinct('category');
    res.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Error fetching categories' });
  }
});

// POST /api/figures - Create new figure (for admin use)
router.post('/', async (req, res) => {
  try {
    const { name, category } = req.body;
    
    if (!name || !category) {
      return res.status(400).json({ error: 'Name and category are required' });
    }
    
    // Get next ID
    const lastFigure = await Figure.findOne().sort({ id: -1 });
    const nextId = lastFigure ? lastFigure.id + 1 : 1;
    
    const figure = new Figure({
      id: nextId,
      name: name.trim(),
      category: category.toLowerCase()
    });
    
    await figure.save();
    res.status(201).json(figure);
  } catch (error) {
    console.error('Error creating figure:', error);
    if (error.code === 11000) {
      res.status(400).json({ error: 'Figure with this name already exists' });
    } else {
      res.status(500).json({ error: 'Error creating figure' });
    }
  }
});

// PUT /api/figures/:id - Update figure
router.put('/:id', async (req, res) => {
  try {
    const { name, category } = req.body;
    
    const figure = await Figure.findOneAndUpdate(
      { id: parseInt(req.params.id) },
      { 
        ...(name && { name: name.trim() }),
        ...(category && { category: category.toLowerCase() })
      },
      { new: true, runValidators: true }
    );
    
    if (!figure) {
      return res.status(404).json({ error: 'Figure not found' });
    }
    
    res.json(figure);
  } catch (error) {
    console.error('Error updating figure:', error);
    res.status(500).json({ error: 'Error updating figure' });
  }
});

// DELETE /api/figures/:id - Delete figure
router.delete('/:id', async (req, res) => {
  try {
    const figure = await Figure.findOneAndDelete({ id: parseInt(req.params.id) });
    
    if (!figure) {
      return res.status(404).json({ error: 'Figure not found' });
    }
    
    res.json({ message: 'Figure deleted successfully' });
  } catch (error) {
    console.error('Error deleting figure:', error);
    res.status(500).json({ error: 'Error deleting figure' });
  }
});

module.exports = router;

const express = require('express');
const router = express.Router();
const Idea = require('../models/Idea');

// GET /api/ideas - Get all ideas
router.get('/', async (req, res) => {
  try {
    const { domain, search, limit = 100, page = 1 } = req.query;
    
    let query = {};
    
    // Filter by domain
    if (domain) {
      query.domain = domain.toLowerCase();
    }
    
    // Search by name
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }
    
    const skip = (page - 1) * limit;
    
    const ideas = await Idea.find(query)
      .sort({ id: 1 })
      .limit(parseInt(limit))
      .skip(skip)
      .lean();
    
    const total = await Idea.countDocuments(query);
    
    res.json({
      ideas,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching ideas:', error);
    res.status(500).json({ error: 'Error fetching ideas' });
  }
});

// GET /api/ideas/:id - Get idea by ID
router.get('/:id', async (req, res) => {
  try {
    const idea = await Idea.findOne({ id: parseInt(req.params.id) }).lean();
    
    if (!idea) {
      return res.status(404).json({ error: 'Idea not found' });
    }
    
    res.json(idea);
  } catch (error) {
    console.error('Error fetching idea:', error);
    res.status(500).json({ error: 'Error fetching idea' });
  }
});

// GET /api/ideas/meta/domains - Get all domains
router.get('/meta/domains', async (req, res) => {
  try {
    const domains = await Idea.distinct('domain');
    res.json({ domains });
  } catch (error) {
    console.error('Error fetching domains:', error);
    res.status(500).json({ error: 'Error fetching domains' });
  }
});

// POST /api/ideas - Create new idea
router.post('/', async (req, res) => {
  try {
    const { name, domain } = req.body;
    
    if (!name || !domain) {
      return res.status(400).json({ error: 'Name and domain are required' });
    }
    
    // Get next ID
    const lastIdea = await Idea.findOne().sort({ id: -1 });
    const nextId = lastIdea ? lastIdea.id + 1 : 1;
    
    const idea = new Idea({
      id: nextId,
      name: name.trim(),
      domain: domain.toLowerCase()
    });
    
    await idea.save();
    res.status(201).json(idea);
  } catch (error) {
    console.error('Error creating idea:', error);
    if (error.code === 11000) {
      res.status(400).json({ error: 'Idea with this name already exists' });
    } else {
      res.status(500).json({ error: 'Error creating idea' });
    }
  }
});

// PUT /api/ideas/:id - Update idea
router.put('/:id', async (req, res) => {
  try {
    const { name, domain } = req.body;
    
    const idea = await Idea.findOneAndUpdate(
      { id: parseInt(req.params.id) },
      { 
        ...(name && { name: name.trim() }),
        ...(domain && { domain: domain.toLowerCase() })
      },
      { new: true, runValidators: true }
    );
    
    if (!idea) {
      return res.status(404).json({ error: 'Idea not found' });
    }
    
    res.json(idea);
  } catch (error) {
    console.error('Error updating idea:', error);
    res.status(500).json({ error: 'Error updating idea' });
  }
});

// DELETE /api/ideas/:id - Delete idea
router.delete('/:id', async (req, res) => {
  try {
    const idea = await Idea.findOneAndDelete({ id: parseInt(req.params.id) });
    
    if (!idea) {
      return res.status(404).json({ error: 'Idea not found' });
    }
    
    res.json({ message: 'Idea deleted successfully' });
  } catch (error) {
    console.error('Error deleting idea:', error);
    res.status(500).json({ error: 'Error deleting idea' });
  }
});

module.exports = router;

const express = require('express');
const router = express.Router();
const Prompt = require('../models/Prompt');

// GET /api/prompts - Get all prompts (with pagination)
router.get('/', async (req, res) => {
  try {
    const { userId = 'anonymous', limit = 10, page = 1 } = req.query;
    
    const skip = (page - 1) * limit;
    
    const prompts = await Prompt.find({ userId })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip)
      .lean();
    
    const total = await Prompt.countDocuments({ userId });
    
    res.json({
      prompts,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching prompts:', error);
    res.status(500).json({ error: 'Error fetching prompts' });
  }
});

// GET /api/prompts/:id - Get prompt by ID
router.get('/:id', async (req, res) => {
  try {
    const prompt = await Prompt.findById(req.params.id).lean();
    
    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }
    
    res.json(prompt);
  } catch (error) {
    console.error('Error fetching prompt:', error);
    res.status(500).json({ error: 'Error fetching prompt' });
  }
});

// POST /api/prompts - Create new prompt
router.post('/', async (req, res) => {
  try {
    const { prompt, figures, ideas, style, userId = 'anonymous' } = req.body;
    
    if (!prompt || !style) {
      return res.status(400).json({ error: 'Prompt and style are required' });
    }
    
    if (!figures || figures.length === 0) {
      if (!ideas || ideas.length === 0) {
        return res.status(400).json({ error: 'At least one figure or idea is required' });
      }
    }
    
    const newPrompt = new Prompt({
      prompt,
      figures: figures || [],
      ideas: ideas || [],
      style,
      userId
    });
    
    await newPrompt.save();
    res.status(201).json(newPrompt);
  } catch (error) {
    console.error('Error creating prompt:', error);
    res.status(500).json({ error: 'Error creating prompt' });
  }
});

// DELETE /api/prompts/:id - Delete prompt
router.delete('/:id', async (req, res) => {
  try {
    const prompt = await Prompt.findByIdAndDelete(req.params.id);
    
    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }
    
    res.json({ message: 'Prompt deleted successfully' });
  } catch (error) {
    console.error('Error deleting prompt:', error);
    res.status(500).json({ error: 'Error deleting prompt' });
  }
});

// GET /api/prompts/stats/:userId - Get user statistics
router.get('/stats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const totalPrompts = await Prompt.countDocuments({ userId });
    
    const styleStats = await Prompt.aggregate([
      { $match: { userId } },
      { $group: { _id: '$style', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    const favoriteStyle = styleStats.length > 0 ? styleStats[0]._id : 'ensayo';
    
    // Calculate days active (days since first prompt)
    const firstPrompt = await Prompt.findOne({ userId }).sort({ createdAt: 1 });
    const daysActive = firstPrompt 
      ? Math.ceil((new Date() - firstPrompt.createdAt) / (1000 * 60 * 60 * 24)) || 1
      : 1;
    
    res.json({
      totalPrompts,
      favoriteStyle,
      daysActive,
      styleBreakdown: styleStats
    });
  } catch (error) {
    console.error('Error fetching prompt stats:', error);
    res.status(500).json({ error: 'Error fetching prompt stats' });
  }
});

module.exports = router;

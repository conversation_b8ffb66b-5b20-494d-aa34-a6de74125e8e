const mongoose = require("mongoose");
const Figure = require("../models/Figure");
const Idea = require("../models/Idea");
require("dotenv").config();

// Sample data - simplified without tags and descriptions
const figuresData = [
  { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>", category: "filosof<PERSON>" },
  { id: 2, name: "<PERSON><PERSON>", category: "arte" },
  { id: 3, name: "<PERSON>", category: "psicología" },
  { id: 4, name: "<PERSON> Woolf", category: "literatura" },
  { id: 5, name: "<PERSON>", category: "arte" },
  { id: 6, name: "<PERSON>", category: "filosofía" },
  { id: 7, name: "<PERSON>", category: "arte" },
  { id: 8, name: "<PERSON><PERSON><PERSON>", category: "psicología" },
  { id: 9, name: "<PERSON>", category: "ciencia" },
  { id: 10, name: "<PERSON>", category: "ciencia" },
  { id: 11, name: "<PERSON><PERSON><PERSON>", category: "filosofía" },
  { id: 12, name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", category: "filosof<PERSON>" },
  { id: 13, name: "<PERSON><PERSON><PERSON>", category: "filosof<PERSON>" },
  { id: 14, name: "<PERSON>mmanu<PERSON><PERSON>", category: "filosof<PERSON>" },
  { id: 15, name: "<PERSON> <PERSON><PERSON><PERSON>", category: "filosof<PERSON>" },
  { id: 16, name: "<PERSON> <PERSON>", category: "fi<PERSON>of<PERSON>" },
  { id: 17, name: "<PERSON> <PERSON> <PERSON>", category: "fi<PERSON><PERSON><PERSON>" },
  { id: 18, name: "<PERSON>-<PERSON> <PERSON>rt<PERSON>", category: "filosof<PERSON>" },
  { id: 19, name: "<PERSON> <PERSON>idegger", category: "filosofía" },
  { id: 20, name: "Ludwig Wittgenstein", category: "filosofía" },
  { id: 21, name: "William Shakespeare", category: "literatura" },
  { id: 22, name: "Miguel de Cervantes", category: "literatura" },
  { id: 23, name: "Fyodor Dostoevsky", category: "literatura" },
  { id: 24, name: "Leo Tolstoy", category: "literatura" },
  { id: 25, name: "James Joyce", category: "literatura" },
  { id: 26, name: "Marcel Proust", category: "literatura" },
  { id: 27, name: "Franz Kafka", category: "literatura" },
  { id: 28, name: "Jorge Luis Borges", category: "literatura" },
  { id: 29, name: "Gabriel García Márquez", category: "literatura" },
  { id: 30, name: "Pablo Neruda", category: "literatura" },
  { id: 31, name: "Isaac Newton", category: "ciencia" },
  { id: 32, name: "Charles Darwin", category: "ciencia" },
  { id: 33, name: "Galileo Galilei", category: "ciencia" },
  { id: 34, name: "Nikola Tesla", category: "ciencia" },
  { id: 35, name: "Stephen Hawking", category: "ciencia" },
  { id: 36, name: "Michelangelo", category: "arte" },
  { id: 37, name: "Vincent van Gogh", category: "arte" },
  { id: 38, name: "Claude Monet", category: "arte" },
  { id: 39, name: "Salvador Dalí", category: "arte" },
  { id: 40, name: "Andy Warhol", category: "arte" },
  { id: 41, name: "Wolfgang Amadeus Mozart", category: "música" },
  { id: 42, name: "Ludwig van Beethoven", category: "música" },
  { id: 43, name: "Johann Sebastian Bach", category: "música" },
  { id: 44, name: "Miles Davis", category: "música" },
  { id: 45, name: "Bob Dylan", category: "música" },
  { id: 46, name: "B.F. Skinner", category: "psicología" },
  { id: 47, name: "Abraham Maslow", category: "psicología" },
  { id: 48, name: "Jean Piaget", category: "psicología" },
  { id: 49, name: "Viktor Frankl", category: "psicología" },
  { id: 50, name: "William James", category: "psicología" },
];

const ideasData = [
  { id: 1, name: "libertad", domain: "filosofía" },
  { id: 2, name: "amor", domain: "filosofía" },
  { id: 3, name: "muerte", domain: "filosofía" },
  { id: 4, name: "tiempo", domain: "filosofía" },
  { id: 5, name: "belleza", domain: "estética" },
  { id: 6, name: "verdad", domain: "epistemología" },
  { id: 7, name: "justicia", domain: "ética" },
  { id: 8, name: "poder", domain: "política" },
  { id: 9, name: "soledad", domain: "psicología" },
  { id: 10, name: "creatividad", domain: "psicología" },
  { id: 11, name: "dolor", domain: "psicología" },
  { id: 12, name: "felicidad", domain: "psicología" },
  { id: 13, name: "identidad", domain: "psicología" },
  { id: 14, name: "memoria", domain: "psicología" },
  { id: 15, name: "esperanza", domain: "psicología" },
  { id: 16, name: "miedo", domain: "psicología" },
  { id: 17, name: "coraje", domain: "ética" },
  { id: 18, name: "sabiduría", domain: "filosofía" },
  { id: 19, name: "ignorancia", domain: "epistemología" },
  { id: 20, name: "fe", domain: "filosofía" },
  { id: 21, name: "duda", domain: "epistemología" },
  { id: 22, name: "razón", domain: "filosofía" },
  { id: 23, name: "emoción", domain: "psicología" },
  { id: 24, name: "intuición", domain: "psicología" },
  { id: 25, name: "conciencia", domain: "filosofía" },
  { id: 26, name: "inconsciente", domain: "psicología" },
  { id: 27, name: "sueños", domain: "psicología" },
  { id: 28, name: "realidad", domain: "filosofía" },
  { id: 29, name: "ilusión", domain: "epistemología" },
  { id: 30, name: "existencia", domain: "filosofía" },
  { id: 31, name: "nada", domain: "filosofía" },
  { id: 32, name: "infinito", domain: "metafísica" },
  { id: 33, name: "caos", domain: "filosofía" },
  { id: 34, name: "orden", domain: "filosofía" },
  { id: 35, name: "armonía", domain: "estética" },
  { id: 36, name: "conflicto", domain: "política" },
  { id: 37, name: "paz", domain: "política" },
  { id: 38, name: "guerra", domain: "política" },
  { id: 39, name: "revolución", domain: "política" },
  { id: 40, name: "tradición", domain: "filosofía" },
  { id: 41, name: "progreso", domain: "filosofía" },
  { id: 42, name: "decadencia", domain: "filosofía" },
  { id: 43, name: "transformación", domain: "filosofía" },
  { id: 44, name: "permanencia", domain: "metafísica" },
  { id: 45, name: "cambio", domain: "filosofía" },
  { id: 46, name: "destino", domain: "filosofía" },
  { id: 47, name: "azar", domain: "filosofía" },
  { id: 48, name: "necesidad", domain: "filosofía" },
  { id: 49, name: "posibilidad", domain: "lógica" },
  { id: 50, name: "imposibilidad", domain: "lógica" },
];

const connectDB = async () => {
  try {
    await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/sabiedumbre"
    );
    console.log("MongoDB Connected for seeding");
  } catch (error) {
    console.error("Error connecting to MongoDB:", error.message);
    process.exit(1);
  }
};

const seedDatabase = async () => {
  try {
    console.log("🌱 Starting database seeding...");

    // Clear existing data
    await Figure.deleteMany({});
    await Idea.deleteMany({});
    console.log("✅ Cleared existing data");

    // Insert figures
    await Figure.insertMany(figuresData);
    console.log(`✅ Inserted ${figuresData.length} figures`);

    // Insert ideas
    await Idea.insertMany(ideasData);
    console.log(`✅ Inserted ${ideasData.length} ideas`);

    console.log("🎉 Database seeding completed successfully!");

    // Show summary
    const figureCount = await Figure.countDocuments();
    const ideaCount = await Idea.countDocuments();

    console.log("\n📊 Database Summary:");
    console.log(`   Figures: ${figureCount}`);
    console.log(`   Ideas: ${ideaCount}`);

    process.exit(0);
  } catch (error) {
    console.error("❌ Error seeding database:", error);
    process.exit(1);
  }
};

const main = async () => {
  await connectDB();
  await seedDatabase();
};

main();

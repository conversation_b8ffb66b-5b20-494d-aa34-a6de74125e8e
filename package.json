{"name": "sabiedumbre", "version": "0.0.1", "description": "A Quasar Project", "productName": "Quasar App", "author": "martindejos <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,css,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"axios": "^1.2.1", "vue-i18n": "^9.0.0", "pinia": "^2.0.11", "@quasar/extras": "^1.16.4", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-router": "^4.0.12"}, "devDependencies": {"eslint": "^8.57.0", "eslint-plugin-vue": "^9.0.0", "vite-plugin-checker": "^0.6.4", "eslint-config-prettier": "^8.1.0", "prettier": "^2.5.1", "@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.9.0", "autoprefixer": "^10.4.2", "postcss": "^8.4.14"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}
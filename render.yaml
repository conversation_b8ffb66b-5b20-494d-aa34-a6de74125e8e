services:
  - type: web
    name: sabiedumbre-backend
    env: node
    region: oregon
    plan: free
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MONGODB_URI
        sync: false
      - key: FRONTEND_URL
        value: https://sabiedumbre.netlify.app
      - key: JWT_SECRET
        sync: false

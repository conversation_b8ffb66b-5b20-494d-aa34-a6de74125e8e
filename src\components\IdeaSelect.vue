<template>
  <q-card class="idea-card" :class="{ 'selected': isSelected }" @click="toggleSelection">
    <q-card-section class="text-center">
      <q-icon 
        :name="getIcon()" 
        size="2rem" 
        :color="isSelected ? 'white' : iconColor"
      />
      <div class="text-h6 q-mt-sm" :class="{ 'text-white': isSelected }">
        {{ item.name }}
      </div>
      <div class="text-caption" :class="{ 'text-white': isSelected, 'text-grey-6': !isSelected }">
        {{ item.description }}
      </div>
      <div class="q-mt-sm">
        <q-chip
          v-for="tag in item.tags?.slice(0, 3)"
          :key="tag"
          size="xs"
          :color="isSelected ? 'white' : 'grey-3'"
          :text-color="isSelected ? 'primary' : 'grey-7'"
          :label="tag"
        />
      </div>
    </q-card-section>
    
    <!-- Selection indicator -->
    <q-icon
      v-if="isSelected"
      name="check_circle"
      color="white"
      size="1.5rem"
      class="selection-indicator"
    />
  </q-card>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: true, // 'figure' or 'idea'
    validator: (value) => ['figure', 'idea'].includes(value)
  },
  selected: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle'])

// Computed
const isSelected = computed(() => props.selected)

const iconColor = computed(() => {
  if (props.type === 'figure') {
    return getCategoryColor(props.item.category)
  } else {
    return getDomainColor(props.item.domain)
  }
})

// Methods
const getIcon = () => {
  if (props.type === 'figure') {
    return getCategoryIcon(props.item.category)
  } else {
    return getDomainIcon(props.item.domain)
  }
}

const getCategoryIcon = (category) => {
  const icons = {
    'filosofía': 'psychology',
    'arte': 'palette',
    'ciencia': 'science',
    'literatura': 'menu_book',
    'psicología': 'psychology_alt'
  }
  return icons[category] || 'person'
}

const getDomainIcon = (domain) => {
  const icons = {
    'filosofía': 'psychology',
    'psicología': 'psychology_alt',
    'estética': 'palette',
    'ética': 'gavel',
    'política': 'account_balance',
    'epistemología': 'lightbulb'
  }
  return icons[domain] || 'lightbulb'
}

const getCategoryColor = (category) => {
  const colors = {
    'filosofía': 'deep-purple',
    'arte': 'pink',
    'ciencia': 'blue',
    'literatura': 'green',
    'psicología': 'orange'
  }
  return colors[category] || 'grey'
}

const getDomainColor = (domain) => {
  const colors = {
    'filosofía': 'deep-purple',
    'psicología': 'orange',
    'estética': 'pink',
    'ética': 'teal',
    'política': 'indigo',
    'epistemología': 'amber'
  }
  return colors[domain] || 'grey'
}

const toggleSelection = () => {
  emit('toggle', props.item)
}
</script>

<style scoped>
.idea-card {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 180px;
}

.idea-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.idea-card.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
}
</style>

<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="min-width: 600px; max-width: 800px;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">🧠 <PERSON>rea<PERSON> Mezcla Creativa</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <div class="row q-gutter-md">
          <!-- Selector de Figuras -->
          <div class="col-12 col-md-6">
            <q-select
              v-model="selectedFigures"
              :options="figureOptions"
              option-label="name"
              option-value="id"
              multiple
              use-chips
              stack-label
              label="Selecciona Figuras (máx. 2)"
              hint="Filósofos, artistas, científicos..."
              :rules="[val => !val || val.length <= 2 || 'Máximo 2 figuras']"
              @update:model-value="validateSelection"
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-icon :name="getCategoryIcon(scope.opt.category)" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.name }}</q-item-label>
                    <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>

          <!-- Selector de Ideas -->
          <div class="col-12 col-md-6">
            <q-select
              v-model="selectedIdeas"
              :options="ideaOptions"
              option-label="name"
              option-value="id"
              multiple
              use-chips
              stack-label
              label="Selecciona Ideas (máx. 2)"
              hint="Conceptos, temas, emociones..."
              :rules="[val => !val || val.length <= 2 || 'Máximo 2 ideas']"
              @update:model-value="validateSelection"
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-icon :name="getDomainIcon(scope.opt.domain)" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.name }}</q-item-label>
                    <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>
        </div>

        <!-- Selector de Estilo -->
        <div class="q-mt-md">
          <q-select
            v-model="selectedStyle"
            :options="styleOptions"
            option-label="label"
            option-value="value"
            label="Estilo del texto"
            hint="Elige cómo quieres que se presente el texto"
          />
        </div>

        <!-- Tags combinados -->
        <div v-if="combinedTags.length > 0" class="q-mt-md">
          <q-chip
            v-for="tag in combinedTags.slice(0, 8)"
            :key="tag"
            size="sm"
            color="primary"
            text-color="white"
            :label="tag"
          />
        </div>
      </q-card-section>

      <!-- Prompt Generado -->
      <q-card-section v-if="generatedPrompt" class="bg-grey-1">
        <div class="text-subtitle2 q-mb-sm">📝 Prompt Generado:</div>
        <div class="text-body2 q-pa-md bg-white rounded-borders">
          {{ generatedPrompt }}
        </div>
        
        <div class="row q-mt-md q-gutter-sm">
          <q-btn
            size="sm"
            color="primary"
            icon="content_copy"
            label="Copiar"
            @click="copyPrompt"
          />
          <q-btn
            size="sm"
            color="secondary"
            icon="refresh"
            label="Regenerar"
            @click="generateNewPrompt"
          />
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn flat label="Cancelar" v-close-popup />
        <q-btn
          color="primary"
          label="Generar Texto"
          :disable="!canGenerate"
          @click="generatePrompt"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useGeneratePrompt } from '../composables/useGeneratePrompt'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  figures: {
    type: Array,
    default: () => []
  },
  ideas: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'prompt-generated'])

// Composables
const $q = useQuasar()
const { generateStyledPrompt, getCombinedTags } = useGeneratePrompt()

// Reactive data
const selectedFigures = ref([])
const selectedIdeas = ref([])
const selectedStyle = ref('ensayo')
const generatedPrompt = ref('')

// Computed
const isOpen = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const figureOptions = computed(() => props.figures)
const ideaOptions = computed(() => props.ideas)

const styleOptions = [
  { label: '📖 Ensayo Filosófico', value: 'ensayo' },
  { label: '💬 Diálogo Imaginario', value: 'dialogo' },
  { label: '📚 Narrativa Corta', value: 'narrativa' },
  { label: '🎭 Poema Lírico', value: 'poema' }
]

const canGenerate = computed(() => {
  return selectedFigures.value.length > 0 || selectedIdeas.value.length > 0
})

const combinedTags = computed(() => {
  return getCombinedTags(selectedFigures.value, selectedIdeas.value)
})

// Methods
const getCategoryIcon = (category) => {
  const icons = {
    'filosofía': 'psychology',
    'arte': 'palette',
    'ciencia': 'science',
    'literatura': 'menu_book',
    'psicología': 'psychology_alt'
  }
  return icons[category] || 'person'
}

const getDomainIcon = (domain) => {
  const icons = {
    'filosofía': 'psychology',
    'psicología': 'psychology_alt',
    'estética': 'palette',
    'ética': 'gavel',
    'política': 'account_balance',
    'epistemología': 'lightbulb'
  }
  return icons[domain] || 'lightbulb'
}

const validateSelection = () => {
  if (selectedFigures.value.length > 2) {
    selectedFigures.value = selectedFigures.value.slice(0, 2)
    $q.notify({
      type: 'warning',
      message: 'Solo puedes seleccionar máximo 2 figuras'
    })
  }
  if (selectedIdeas.value.length > 2) {
    selectedIdeas.value = selectedIdeas.value.slice(0, 2)
    $q.notify({
      type: 'warning',
      message: 'Solo puedes seleccionar máximo 2 ideas'
    })
  }
}

const generatePrompt = () => {
  if (!canGenerate.value) {
    $q.notify({
      type: 'warning',
      message: 'Selecciona al menos una figura o una idea'
    })
    return
  }

  generatedPrompt.value = generateStyledPrompt(
    selectedFigures.value,
    selectedIdeas.value,
    selectedStyle.value
  )

  emit('prompt-generated', {
    prompt: generatedPrompt.value,
    figures: selectedFigures.value,
    ideas: selectedIdeas.value,
    style: selectedStyle.value
  })
}

const generateNewPrompt = () => {
  generatePrompt()
}

const copyPrompt = async () => {
  try {
    await navigator.clipboard.writeText(generatedPrompt.value)
    $q.notify({
      type: 'positive',
      message: 'Prompt copiado al portapapeles'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Error al copiar el prompt'
    })
  }
}

// Watch for changes in selections to auto-generate
watch([selectedFigures, selectedIdeas, selectedStyle], () => {
  if (canGenerate.value) {
    generatePrompt()
  }
}, { deep: true })
</script>

<style scoped>
.rounded-borders {
  border-radius: 8px;
}
</style>

<template>
  <q-dialog
    v-model="dialogOpen"
    persistent
    transition-show="slide-up"
    transition-hide="slide-down"
    class="modern-dialog"
  >
    <q-card class="modern-modal-card">
      <!-- Header -->
      <q-card-section class="modal-header">
        <div class="modal-title">
          <span class="brain-icon">🧠</span>
          <h2 class="text-gradient">Crear Mezcla Creativa</h2>
        </div>
        <q-btn
          icon="close"
          flat
          round
          size="lg"
          class="close-btn-right"
          @click="closeModal"
        />
      </q-card-section>

      <!-- Content -->
      <q-card-section class="modal-content">
        <div class="main-layout">
          <!-- Left Side: Selectors -->
          <div class="selectors-side">
            <!-- Selector 1: Figuras -->
            <div class="selector-section">
              <h3 class="selector-title">
                <q-icon name="people" class="q-mr-sm" />
                1. Selecciona Figuras (máx. 2)
              </h3>
              <q-select
                v-model="selectedFigures"
                :options="figures"
                option-label="name"
                option-value="id"
                multiple
                use-chips
                outlined
                label="Buscar y seleccionar figuras..."
                class="modern-select"
                :rules="[val => !val || val.length <= 2 || 'Máximo 2 figuras']"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <q-icon :name="getCategoryIcon(scope.opt.category)" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>

            <!-- Selector 2: Ideas -->
            <div class="selector-section">
              <h3 class="selector-title">
                <q-icon name="lightbulb" class="q-mr-sm" />
                2. Selecciona Ideas (máx. 2)
              </h3>
              <q-select
                v-model="selectedIdeas"
                :options="ideas"
                option-label="name"
                option-value="id"
                multiple
                use-chips
                outlined
                label="Buscar y seleccionar ideas..."
                class="modern-select"
                :rules="[val => !val || val.length <= 2 || 'Máximo 2 ideas']"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <q-icon :name="getDomainIcon(scope.opt.domain)" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>

            <!-- Selector 3: Estilo -->
            <div class="selector-section">
              <h3 class="selector-title">
                <q-icon name="palette" class="q-mr-sm" />
                3. Selecciona Estilo
              </h3>
              <q-select
                v-model="selectedStyle"
                :options="styleOptions"
                option-label="label"
                option-value="value"
                outlined
                label="Elige el estilo del texto..."
                class="modern-select"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <q-icon :name="scope.opt.icon" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.label }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>

          <!-- Right Side: Shopping Cart -->
          <div class="cart-side">
            <div class="cart-container glass-card">
              <h3 class="cart-title">
                <q-icon name="shopping_cart" class="q-mr-sm" />
                Tu Mezcla
              </h3>

              <!-- Cart Items -->
              <div class="cart-items">
                <!-- Selected Figures -->
                <div v-if="selectedFigures.length > 0" class="cart-section">
                  <h4 class="cart-section-title">
                    <q-icon name="people" size="1rem" />
                    Figuras ({{ selectedFigures.length }}/2)
                  </h4>
                  <div class="cart-item" v-for="figure in selectedFigures" :key="figure.id">
                    <div class="item-info">
                      <q-icon :name="getCategoryIcon(figure.category)" class="item-icon" />
                      <div>
                        <div class="item-name">{{ figure.name }}</div>
                        <div class="item-category">{{ figure.category }}</div>
                      </div>
                    </div>
                    <q-btn
                      icon="close"
                      flat
                      round
                      size="sm"
                      @click="removeFigure(figure)"
                      class="remove-btn"
                    />
                  </div>
                </div>

                <!-- Selected Ideas -->
                <div v-if="selectedIdeas.length > 0" class="cart-section">
                  <h4 class="cart-section-title">
                    <q-icon name="lightbulb" size="1rem" />
                    Ideas ({{ selectedIdeas.length }}/2)
                  </h4>
                  <div class="cart-item" v-for="idea in selectedIdeas" :key="idea.id">
                    <div class="item-info">
                      <q-icon :name="getDomainIcon(idea.domain)" class="item-icon" />
                      <div>
                        <div class="item-name">{{ idea.name }}</div>
                        <div class="item-category">{{ idea.domain }}</div>
                      </div>
                    </div>
                    <q-btn
                      icon="close"
                      flat
                      round
                      size="sm"
                      @click="removeIdea(idea)"
                      class="remove-btn"
                    />
                  </div>
                </div>

                <!-- Selected Style -->
                <div v-if="selectedStyle" class="cart-section">
                  <h4 class="cart-section-title">
                    <q-icon name="palette" size="1rem" />
                    Estilo
                  </h4>
                  <div class="cart-item">
                    <div class="item-info">
                      <q-icon :name="getStyleIcon(selectedStyle)" class="item-icon" />
                      <div>
                        <div class="item-name">{{ getStyleLabel(selectedStyle) }}</div>
                        <div class="item-category">{{ getStyleDescription(selectedStyle) }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Empty Cart -->
                <div v-if="cartIsEmpty" class="empty-cart">
                  <q-icon name="shopping_cart" size="3rem" color="grey-5" />
                  <p>Selecciona elementos para crear tu mezcla</p>
                </div>
              </div>

              <!-- Cart Summary -->
              <div class="cart-summary">
                <div class="summary-item">
                  <span>Total de elementos:</span>
                  <span class="summary-value">{{ totalItems }}</span>
                </div>
                <div class="summary-item">
                  <span>Estado:</span>
                  <span class="summary-value" :class="cartStatusClass">{{ cartStatus }}</span>
                </div>
              </div>

              <!-- Generate Button -->
              <q-btn
                class="generate-btn btn-modern btn-gradient"
                :disable="!canGenerate"
                @click="generatePrompt"
                size="lg"
                icon="auto_awesome"
                label="Generar Prompt"
                no-caps
                :loading="isGenerating"
              />
            </div>
          </div>
        </div>
      </q-card-section>


    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useGeneratePrompt } from '../composables/useGeneratePrompt'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  figures: {
    type: Array,
    default: () => []
  },
  ideas: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'prompt-generated'])

// Composables
const $q = useQuasar()
const { generateStyledPrompt } = useGeneratePrompt()

// Reactive data
const selectedFigures = ref([])
const selectedIdeas = ref([])
const selectedStyle = ref('ensayo')
const generatedPrompt = ref('')
const isGenerating = ref(false)

// Computed
const dialogOpen = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
    if (!val) {
      // Reset when closing
      resetModal()
    }
  }
})

const styleOptions = [
  {
    label: 'Ensayo Filosófico',
    value: 'ensayo',
    icon: 'menu_book',
    description: 'Reflexión profunda y estructurada'
  },
  {
    label: 'Diálogo Imaginario',
    value: 'dialogo',
    icon: 'chat',
    description: 'Conversación entre las figuras'
  },
  {
    label: 'Narrativa Corta',
    value: 'narrativa',
    icon: 'auto_stories',
    description: 'Historia que explore los temas'
  },
  {
    label: 'Poema Lírico',
    value: 'poema',
    icon: 'lyrics',
    description: 'Expresión poética y evocativa'
  }
]

const canGenerate = computed(() => {
  return (selectedFigures.value.length > 0 || selectedIdeas.value.length > 0) && selectedStyle.value
})

// Cart computed properties
const cartIsEmpty = computed(() => {
  return selectedFigures.value.length === 0 && selectedIdeas.value.length === 0 && !selectedStyle.value
})

const totalItems = computed(() => {
  return selectedFigures.value.length + selectedIdeas.value.length + (selectedStyle.value ? 1 : 0)
})

const cartStatus = computed(() => {
  if (canGenerate.value) {
    return 'Listo para generar'
  } else if (selectedStyle.value && (selectedFigures.value.length > 0 || selectedIdeas.value.length > 0)) {
    return 'Completo'
  } else if (selectedFigures.value.length > 0 || selectedIdeas.value.length > 0) {
    return 'Falta seleccionar estilo'
  } else if (selectedStyle.value) {
    return 'Falta seleccionar contenido'
  } else {
    return 'Vacío'
  }
})

const cartStatusClass = computed(() => {
  if (canGenerate.value) {
    return 'text-positive'
  } else if (totalItems.value > 0) {
    return 'text-warning'
  } else {
    return 'text-grey'
  }
})

// Methods
const getCategoryIcon = (category) => {
  const icons = {
    'filosofía': 'psychology',
    'arte': 'palette',
    'ciencia': 'science',
    'literatura': 'menu_book',
    'psicología': 'psychology_alt'
  }
  return icons[category] || 'person'
}

const getDomainIcon = (domain) => {
  const icons = {
    'filosofía': 'psychology',
    'psicología': 'psychology_alt',
    'estética': 'palette',
    'ética': 'gavel',
    'política': 'account_balance',
    'epistemología': 'lightbulb'
  }
  return icons[domain] || 'lightbulb'
}

const isSelected = (item, type) => {
  if (type === 'figure') {
    return selectedFigures.value.some(f => f.id === item.id)
  } else {
    return selectedIdeas.value.some(i => i.id === item.id)
  }
}

const toggleSelection = (item, type) => {
  if (type === 'figure') {
    const index = selectedFigures.value.findIndex(f => f.id === item.id)
    if (index > -1) {
      selectedFigures.value.splice(index, 1)
    } else {
      if (selectedFigures.value.length >= 2) {
        alert('Solo puedes seleccionar máximo 2 figuras')
        return
      }
      selectedFigures.value.push(item)
    }
  } else {
    const index = selectedIdeas.value.findIndex(i => i.id === item.id)
    if (index > -1) {
      selectedIdeas.value.splice(index, 1)
    } else {
      if (selectedIdeas.value.length >= 2) {
        alert('Solo puedes seleccionar máximo 2 ideas')
        return
      }
      selectedIdeas.value.push(item)
    }
  }
}

const resetModal = () => {
  selectedFigures.value = []
  selectedIdeas.value = []
  selectedStyle.value = 'ensayo'
  generatedPrompt.value = ''
  isGenerating.value = false
}

const closeModal = () => {
  emit('update:modelValue', false)
}

// Cart management methods
const removeFigure = (figure) => {
  const index = selectedFigures.value.findIndex(f => f.id === figure.id)
  if (index > -1) {
    selectedFigures.value.splice(index, 1)
  }
}

const removeIdea = (idea) => {
  const index = selectedIdeas.value.findIndex(i => i.id === idea.id)
  if (index > -1) {
    selectedIdeas.value.splice(index, 1)
  }
}

// Style helper methods
const getStyleIcon = (styleValue) => {
  const style = styleOptions.find(s => s.value === styleValue)
  return style ? style.icon : 'help'
}

const getStyleLabel = (styleValue) => {
  const style = styleOptions.find(s => s.value === styleValue)
  return style ? style.label : ''
}

const getStyleDescription = (styleValue) => {
  const style = styleOptions.find(s => s.value === styleValue)
  return style ? style.description : ''
}

const generatePrompt = async () => {
  if (!canGenerate.value) {
    $q.notify({
      type: 'warning',
      message: 'Completa tu selección: necesitas al menos una figura o idea, y un estilo',
      position: 'top'
    })
    return
  }

  isGenerating.value = true

  try {
    // Simular un pequeño delay para mostrar el loading
    await new Promise(resolve => setTimeout(resolve, 1000))

    const prompt = generateStyledPrompt(
      selectedFigures.value,
      selectedIdeas.value,
      selectedStyle.value
    )

    generatedPrompt.value = prompt

    // Emit the generated prompt data
    emit('prompt-generated', {
      prompt: prompt,
      figures: [...selectedFigures.value],
      ideas: [...selectedIdeas.value],
      style: selectedStyle.value,
      timestamp: new Date().toISOString()
    })

    $q.notify({
      type: 'positive',
      message: '¡Prompt generado exitosamente! 🎉',
      icon: 'auto_awesome',
      position: 'top'
    })

    // Cerrar modal después de generar
    setTimeout(() => {
      closeModal()
    }, 1500)

  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Error al generar el prompt',
      position: 'top'
    })
  } finally {
    isGenerating.value = false
  }
}

// Watch for validation
watch([selectedFigures, selectedIdeas], () => {
  // Validate limits
  if (selectedFigures.value.length > 2) {
    selectedFigures.value = selectedFigures.value.slice(0, 2)
    $q.notify({
      type: 'warning',
      message: 'Solo puedes seleccionar máximo 2 figuras',
      position: 'top'
    })
  }
  if (selectedIdeas.value.length > 2) {
    selectedIdeas.value = selectedIdeas.value.slice(0, 2)
    $q.notify({
      type: 'warning',
      message: 'Solo puedes seleccionar máximo 2 ideas',
      position: 'top'
    })
  }
}, { deep: true })
</script>

<style scoped>
.modern-dialog {
  backdrop-filter: blur(10px);
}

.modern-modal-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  height: 100vh;
  max-height: 100vh;
  width: 95vw;
  max-width: 1400px;
  margin: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.body--dark .modern-modal-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Header */
.modal-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.body--dark .modal-header {
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.close-btn-right {
  color: #64748b;
  transition: all 0.2s ease;
}

.close-btn-right:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brain-icon {
  font-size: 2.5rem;
  animation: pulse 2s infinite;
}

.modal-title h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
}

.close-btn {
  color: #64748b;
  transition: var(--transition);
}

.close-btn:hover {
  color: #ef4444;
  transform: scale(1.1);
}

/* Content */
.modal-content {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 120px);
}

/* Main Layout */
.main-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 60vh;
}

/* Selectors Side */
.selectors-side {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.selector-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.body--dark .selector-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.selector-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.body--dark .selector-title {
  color: #f1f5f9;
}

.modern-select {
  margin-top: 1rem;
}

.modern-select .q-field__control {
  border-radius: var(--border-radius);
  background: white;
  min-height: 60px;
}

.body--dark .modern-select .q-field__control {
  background: rgba(255, 255, 255, 0.05);
}

/* Cart Side */
.cart-side {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.cart-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.body--dark .cart-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cart-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}

.body--dark .cart-title {
  color: #f1f5f9;
}

.cart-items {
  flex: 1;
  margin-bottom: 2rem;
}

.cart-section {
  margin-bottom: 2rem;
}

.cart-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cart-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: white;
  border-radius: var(--border-radius);
  margin-bottom: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
}

.body--dark .cart-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cart-item:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-soft);
}

.item-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.item-icon {
  color: #3b82f6;
}

.item-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.body--dark .item-name {
  color: #f1f5f9;
}

.item-category {
  font-size: 0.8rem;
  color: #64748b;
  text-transform: capitalize;
}

.remove-btn {
  color: #64748b;
  transition: var(--transition);
}

.remove-btn:hover {
  color: #ef4444;
  transform: scale(1.1);
}

.empty-cart {
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
}

.empty-cart p {
  margin-top: 1rem;
  font-style: italic;
}

.cart-summary {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
  margin-bottom: 2rem;
}

.body--dark .cart-summary {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.summary-value {
  font-weight: 600;
}

.generate-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--border-radius);
}

.generate-btn:disabled {
  opacity: 0.5;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.item-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  text-align: center;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.body--dark .item-card {
  background: rgba(255, 255, 255, 0.05);
}

.item-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.item-card.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

.item-icon {
  margin-bottom: 1rem;
}

.item-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.item-card p {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.item-tags {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  justify-content: center;
}

.selection-check {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

/* Style Section */
.style-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 2rem;
}

.body--dark .style-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.style-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  text-align: center;
}

.body--dark .style-card {
  background: rgba(255, 255, 255, 0.05);
}

.style-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

.style-card.selected {
  border-color: #10b981;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.style-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.style-card p {
  font-size: 0.8rem;
  opacity: 0.8;
  margin: 0;
}

/* Prompt Section */
.prompt-section {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.5);
}

.body--dark .prompt-section {
  background: rgba(0, 0, 0, 0.1);
}

.prompt-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.prompt-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.body--dark .prompt-title {
  color: #f1f5f9;
}

.prompt-text {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.body--dark .prompt-text {
  background: rgba(255, 255, 255, 0.05);
  color: #e5e7eb;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Footer */
.modal-footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: sticky;
  bottom: 0;
}

.body--dark .modal-footer {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar */
.items-grid::-webkit-scrollbar {
  width: 6px;
}

.items-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.items-grid::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 3px;
}

/* Responsive */
@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cart-side {
    position: static;
  }

  .cart-container {
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .modal-content {
    padding: 1rem;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-title h2 {
    font-size: 2rem;
  }

  .selector-section {
    padding: 1.5rem;
  }

  .cart-container {
    padding: 1.5rem;
  }

  .selectors-side {
    gap: 1.5rem;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
</style>


<template>
  <q-dialog
    v-model="dialogOpen"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
    class="modern-dialog"
  >
    <q-card class="modern-modal-card">
      <!-- Header -->
      <q-card-section class="modal-header">
        <div class="modal-title">
          <span class="brain-icon">🧠</span>
          <h2 class="text-gradient">Crear Mezcla Creativa</h2>
        </div>
        <q-btn
          icon="close"
          flat
          round
          size="lg"
          class="close-btn"
          @click="closeModal"
        />
      </q-card-section>

      <!-- Content -->
      <q-card-section class="modal-content">
        <div class="selection-grid">
          <!-- Figuras Section -->
          <div class="selection-section">
            <h3 class="section-title">
              <q-icon name="people" class="q-mr-sm" />
              Figuras Históricas
            </h3>
            <p class="section-subtitle">Selecciona hasta 2 figuras</p>

            <div class="items-grid">
              <div
                v-for="figure in figures"
                :key="figure.id"
                class="item-card"
                :class="{ 'selected': isSelected(figure, 'figure') }"
                @click="toggleSelection(figure, 'figure')"
              >
                <div class="item-icon">
                  <q-icon :name="getCategoryIcon(figure.category)" size="2rem" />
                </div>
                <h4>{{ figure.name }}</h4>
                <p>{{ figure.description }}</p>
                <div class="item-tags">
                  <q-chip
                    v-for="tag in figure.tags.slice(0, 2)"
                    :key="tag"
                    size="xs"
                    :label="tag"
                  />
                </div>
                <q-icon
                  v-if="isSelected(figure, 'figure')"
                  name="check_circle"
                  class="selection-check"
                  color="primary"
                  size="1.5rem"
                />
              </div>
            </div>
          </div>

          <!-- Ideas Section -->
          <div class="selection-section">
            <h3 class="section-title">
              <q-icon name="lightbulb" class="q-mr-sm" />
              Conceptos e Ideas
            </h3>
            <p class="section-subtitle">Selecciona hasta 2 conceptos</p>

            <div class="items-grid">
              <div
                v-for="idea in ideas"
                :key="idea.id"
                class="item-card"
                :class="{ 'selected': isSelected(idea, 'idea') }"
                @click="toggleSelection(idea, 'idea')"
              >
                <div class="item-icon">
                  <q-icon :name="getDomainIcon(idea.domain)" size="2rem" />
                </div>
                <h4>{{ idea.name }}</h4>
                <p>{{ idea.description }}</p>
                <div class="item-tags">
                  <q-chip
                    v-for="tag in idea.tags.slice(0, 2)"
                    :key="tag"
                    size="xs"
                    :label="tag"
                  />
                </div>
                <q-icon
                  v-if="isSelected(idea, 'idea')"
                  name="check_circle"
                  class="selection-check"
                  color="secondary"
                  size="1.5rem"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Style Selection -->
        <div class="style-section">
          <h3 class="section-title">
            <q-icon name="palette" class="q-mr-sm" />
            Estilo del Texto
          </h3>
          <div class="style-grid">
            <div
              v-for="style in styleOptions"
              :key="style.value"
              class="style-card"
              :class="{ 'selected': selectedStyle === style.value }"
              @click="selectedStyle = style.value"
            >
              <q-icon :name="style.icon" size="2rem" />
              <h4>{{ style.label }}</h4>
              <p>{{ style.description }}</p>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Prompt Result -->
      <q-card-section v-if="generatedPrompt" class="prompt-section">
        <div class="prompt-container glass-card">
          <h3 class="prompt-title">
            <q-icon name="auto_awesome" class="q-mr-sm" />
            Prompt Generado
          </h3>
          <div class="prompt-text">
            {{ generatedPrompt }}
          </div>
          <div class="prompt-actions">
            <q-btn
              class="btn-modern"
              color="primary"
              icon="content_copy"
              label="Copiar"
              @click="copyPrompt"
              no-caps
            />
            <q-btn
              class="btn-modern q-ml-sm"
              color="secondary"
              icon="refresh"
              label="Regenerar"
              @click="generateNewPrompt"
              outline
              no-caps
            />
          </div>
        </div>
      </q-card-section>

      <!-- Footer Actions -->
      <q-card-actions class="modal-footer">
        <q-btn
          flat
          label="Cancelar"
          @click="closeModal"
          size="lg"
          no-caps
        />
        <q-space />
        <q-btn
          class="btn-modern btn-gradient"
          label="Generar Prompt"
          :disable="!canGenerate"
          @click="generatePrompt"
          size="lg"
          no-caps
          icon="auto_awesome"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useGeneratePrompt } from '../composables/useGeneratePrompt'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  figures: {
    type: Array,
    default: () => []
  },
  ideas: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'prompt-generated'])

// Composables
const $q = useQuasar()
const { generateStyledPrompt } = useGeneratePrompt()

// Reactive data
const selectedFigures = ref([])
const selectedIdeas = ref([])
const selectedStyle = ref('ensayo')
const generatedPrompt = ref('')

// Computed
const dialogOpen = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
    if (!val) {
      // Reset when closing
      resetModal()
    }
  }
})

const styleOptions = [
  {
    label: 'Ensayo Filosófico',
    value: 'ensayo',
    icon: 'menu_book',
    description: 'Reflexión profunda y estructurada'
  },
  {
    label: 'Diálogo Imaginario',
    value: 'dialogo',
    icon: 'chat',
    description: 'Conversación entre las figuras'
  },
  {
    label: 'Narrativa Corta',
    value: 'narrativa',
    icon: 'auto_stories',
    description: 'Historia que explore los temas'
  },
  {
    label: 'Poema Lírico',
    value: 'poema',
    icon: 'lyrics',
    description: 'Expresión poética y evocativa'
  }
]

const canGenerate = computed(() => {
  return selectedFigures.value.length > 0 || selectedIdeas.value.length > 0
})

// Methods
const getCategoryIcon = (category) => {
  const icons = {
    'filosofía': 'psychology',
    'arte': 'palette',
    'ciencia': 'science',
    'literatura': 'menu_book',
    'psicología': 'psychology_alt'
  }
  return icons[category] || 'person'
}

const getDomainIcon = (domain) => {
  const icons = {
    'filosofía': 'psychology',
    'psicología': 'psychology_alt',
    'estética': 'palette',
    'ética': 'gavel',
    'política': 'account_balance',
    'epistemología': 'lightbulb'
  }
  return icons[domain] || 'lightbulb'
}

const isSelected = (item, type) => {
  if (type === 'figure') {
    return selectedFigures.value.some(f => f.id === item.id)
  } else {
    return selectedIdeas.value.some(i => i.id === item.id)
  }
}

const toggleSelection = (item, type) => {
  if (type === 'figure') {
    const index = selectedFigures.value.findIndex(f => f.id === item.id)
    if (index > -1) {
      selectedFigures.value.splice(index, 1)
    } else {
      if (selectedFigures.value.length >= 2) {
        alert('Solo puedes seleccionar máximo 2 figuras')
        return
      }
      selectedFigures.value.push(item)
    }
  } else {
    const index = selectedIdeas.value.findIndex(i => i.id === item.id)
    if (index > -1) {
      selectedIdeas.value.splice(index, 1)
    } else {
      if (selectedIdeas.value.length >= 2) {
        alert('Solo puedes seleccionar máximo 2 ideas')
        return
      }
      selectedIdeas.value.push(item)
    }
  }
}

const resetModal = () => {
  selectedFigures.value = []
  selectedIdeas.value = []
  selectedStyle.value = 'ensayo'
  generatedPrompt.value = ''
}

const closeModal = () => {
  emit('update:modelValue', false)
}

const generatePrompt = () => {
  if (!canGenerate.value) {
    alert('Selecciona al menos una figura o una idea')
    return
  }

  const prompt = generateStyledPrompt(
    selectedFigures.value,
    selectedIdeas.value,
    selectedStyle.value
  )

  generatedPrompt.value = prompt

  // Emit the generated prompt data
  emit('prompt-generated', {
    prompt: prompt,
    figures: [...selectedFigures.value],
    ideas: [...selectedIdeas.value],
    style: selectedStyle.value,
    timestamp: new Date().toISOString()
  })

  alert('Prompt generado exitosamente')
}

const generateNewPrompt = () => {
  generatePrompt()
}

const copyPrompt = async () => {
  try {
    await navigator.clipboard.writeText(generatedPrompt.value)
    alert('Prompt copiado al portapapeles')
  } catch (error) {
    alert('Error al copiar el prompt')
  }
}

// Auto-generate when selections change
watch([selectedFigures, selectedIdeas, selectedStyle], () => {
  if (canGenerate.value && (selectedFigures.value.length > 0 || selectedIdeas.value.length > 0)) {
    generatePrompt()
  } else {
    generatedPrompt.value = ''
  }
}, { deep: true })
</script>

<style scoped>
.modern-dialog {
  backdrop-filter: blur(10px);
}

.modern-modal-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0;
  box-shadow: none;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.body--dark .modern-modal-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Header */
.modal-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.body--dark .modal-header {
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brain-icon {
  font-size: 2.5rem;
  animation: pulse 2s infinite;
}

.modal-title h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
}

.close-btn {
  color: #64748b;
  transition: var(--transition);
}

.close-btn:hover {
  color: #ef4444;
  transform: scale(1.1);
}

/* Content */
.modal-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  flex: 1;
  overflow-y: auto;
}

.selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.selection-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.body--dark .selection-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.body--dark .section-title {
  color: #f1f5f9;
}

.section-subtitle {
  color: #64748b;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.item-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  text-align: center;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.body--dark .item-card {
  background: rgba(255, 255, 255, 0.05);
}

.item-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.item-card.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

.item-icon {
  margin-bottom: 1rem;
}

.item-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.item-card p {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.item-tags {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  justify-content: center;
}

.selection-check {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

/* Style Section */
.style-section {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 2rem;
}

.body--dark .style-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.style-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  text-align: center;
}

.body--dark .style-card {
  background: rgba(255, 255, 255, 0.05);
}

.style-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

.style-card.selected {
  border-color: #10b981;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.style-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.style-card p {
  font-size: 0.8rem;
  opacity: 0.8;
  margin: 0;
}

/* Prompt Section */
.prompt-section {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.5);
}

.body--dark .prompt-section {
  background: rgba(0, 0, 0, 0.1);
}

.prompt-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.prompt-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.body--dark .prompt-title {
  color: #f1f5f9;
}

.prompt-text {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.body--dark .prompt-text {
  background: rgba(255, 255, 255, 0.05);
  color: #e5e7eb;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Footer */
.modal-footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: sticky;
  bottom: 0;
}

.body--dark .modal-footer {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar */
.items-grid::-webkit-scrollbar {
  width: 6px;
}

.items-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.items-grid::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 3px;
}

/* Responsive */
@media (max-width: 768px) {
  .selection-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .modal-content {
    padding: 1rem;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-title h2 {
    font-size: 2rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
    max-height: 300px;
  }

  .style-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
</style>


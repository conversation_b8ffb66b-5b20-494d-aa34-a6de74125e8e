import { ref, computed } from 'vue'
import { figuresAPI, ideasAPI, promptsAPI } from '../services/api'
import { useQuasar } from 'quasar'

export function useAPI() {
  const $q = useQuasar()
  
  // State
  const figures = ref([])
  const ideas = ref([])
  const prompts = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  // Computed
  const figureCategories = computed(() => {
    const categories = [...new Set(figures.value.map(f => f.category))]
    return categories.map(category => ({
      name: category,
      count: figures.value.filter(f => f.category === category).length
    }))
  })
  
  const ideaDomains = computed(() => {
    const domains = [...new Set(ideas.value.map(i => i.domain))]
    return domains.map(domain => ({
      name: domain,
      count: ideas.value.filter(i => i.domain === domain).length
    }))
  })
  
  // Error handling
  const handleError = (error, message = 'Error en la operación') => {
    console.error(message, error)
    $q.notify({
      type: 'negative',
      message: error.message || message,
      position: 'top'
    })
  }
  
  // Figures methods
  const loadFigures = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      const response = await figuresAPI.getAll(params)
      figures.value = response.figures || response
      return response
    } catch (err) {
      error.value = err
      handleError(err, 'Error cargando figuras')
      return []
    } finally {
      loading.value = false
    }
  }
  
  const getFigureById = async (id) => {
    try {
      return await figuresAPI.getById(id)
    } catch (err) {
      handleError(err, 'Error cargando figura')
      return null
    }
  }
  
  // Ideas methods
  const loadIdeas = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      const response = await ideasAPI.getAll(params)
      ideas.value = response.ideas || response
      return response
    } catch (err) {
      error.value = err
      handleError(err, 'Error cargando ideas')
      return []
    } finally {
      loading.value = false
    }
  }
  
  const getIdeaById = async (id) => {
    try {
      return await ideasAPI.getById(id)
    } catch (err) {
      handleError(err, 'Error cargando idea')
      return null
    }
  }
  
  // Prompts methods
  const loadPrompts = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      const response = await promptsAPI.getAll(params)
      prompts.value = response.prompts || response
      return response
    } catch (err) {
      error.value = err
      handleError(err, 'Error cargando prompts')
      return []
    } finally {
      loading.value = false
    }
  }
  
  const createPrompt = async (promptData) => {
    try {
      loading.value = true
      const newPrompt = await promptsAPI.create(promptData)
      
      // Add to local prompts array
      prompts.value.unshift(newPrompt)
      
      $q.notify({
        type: 'positive',
        message: 'Prompt guardado exitosamente',
        icon: 'auto_awesome',
        position: 'top'
      })
      
      return newPrompt
    } catch (err) {
      handleError(err, 'Error guardando prompt')
      throw err
    } finally {
      loading.value = false
    }
  }
  
    
  const getPromptStats = async (userId = 'anonymous') => {
    try {
      return await promptsAPI.getStats(userId)
    } catch (err) {
      handleError(err, 'Error cargando estadísticas')
      return {
        totalPrompts: 0,
        favoriteStyle: 'ensayo',
        daysActive: 1
      }
    }
  }
  
  // Initialize data
  const initializeData = async () => {
    await Promise.all([
      loadFigures(),
      loadIdeas()
    ])
  }
  
  // Search methods
  const searchFigures = async (query, category = null) => {
    const params = { search: query }
    if (category) params.category = category
    return await loadFigures(params)
  }
  
  const searchIdeas = async (query, domain = null) => {
    const params = { search: query }
    if (domain) params.domain = domain
    return await loadIdeas(params)
  }
  
  return {
    // State
    figures,
    ideas,
    prompts,
    loading,
    error,
    
    // Computed
    figureCategories,
    ideaDomains,
    
    // Methods
    loadFigures,
    getFigureById,
    loadIdeas,
    getIdeaById,
    loadPrompts,
    createPrompt,
    getPromptStats,
    initializeData,
    searchFigures,
    searchIdeas
  }
}

/**
 * Composable para generar prompts creativos mezclando figuras e ideas
 */
export function useGeneratePrompt() {
  
  /**
   * Genera un prompt creativo combinando figuras e ideas seleccionadas
   * @param {Array} figures - Array de figuras seleccionadas
   * @param {Array} ideas - Array de ideas seleccionadas
   * @returns {string} - Prompt generado
   */
  const generatePrompt = (figures, ideas) => {
    if (!figures.length && !ideas.length) {
      return "Selecciona al menos una figura o una idea para generar un texto creativo.";
    }

    let prompt = "Escribe un texto creativo";
    
    // Agregar figuras al prompt
    if (figures.length > 0) {
      const figureNames = figures.map(f => f.name).join(" y ");
      prompt += ` que combine las perspectivas de ${figureNames}`;
    }
    
    // Agregar ideas al prompt
    if (ideas.length > 0) {
      const ideaNames = ideas.map(i => i.name).join(" y ");
      if (figures.length > 0) {
        prompt += ` con los conceptos de ${ideaNames}`;
      } else {
        prompt += ` explorando los conceptos de ${ideaNames}`;
      }
    }
    
    // Agregar contexto adicional basado en las categorías
    const categories = [...new Set([
      ...figures.map(f => f.category),
      ...ideas.map(i => i.domain)
    ])];
    
    if (categories.length > 1) {
      prompt += `. Integra elementos de ${categories.join(", ")}`;
    }
    
    prompt += ". El texto debe ser reflexivo, original y mostrar conexiones inesperadas entre estos elementos.";
    
    return prompt;
  };

  /**
   * Genera un prompt más específico para un estilo particular
   * @param {Array} figures - Array de figuras seleccionadas
   * @param {Array} ideas - Array de ideas seleccionadas
   * @param {string} style - Estilo del texto (ensayo, diálogo, narrativa, poema)
   * @returns {string} - Prompt generado con estilo específico
   */
  const generateStyledPrompt = (figures, ideas, style = 'ensayo') => {
    const basePrompt = generatePrompt(figures, ideas);
    
    const styleInstructions = {
      ensayo: "Escríbelo como un ensayo filosófico profundo y reflexivo.",
      dialogo: "Preséntalo como un diálogo imaginario entre estas figuras.",
      narrativa: "Créalo como una narrativa corta que explore estos temas.",
      poema: "Exprésalo en forma de poema lírico y evocativo."
    };
    
    return basePrompt + " " + (styleInstructions[style] || styleInstructions.ensayo);
  };

  /**
   * Genera múltiples variaciones de prompts
   * @param {Array} figures - Array de figuras seleccionadas
   * @param {Array} ideas - Array de ideas seleccionadas
   * @returns {Array} - Array de prompts variados
   */
  const generatePromptVariations = (figures, ideas) => {
    const styles = ['ensayo', 'dialogo', 'narrativa', 'poema'];
    return styles.map(style => ({
      style,
      prompt: generateStyledPrompt(figures, ideas, style)
    }));
  };

  /**
   * Obtiene tags combinados de figuras e ideas para inspiración adicional
   * @param {Array} figures - Array de figuras seleccionadas
   * @param {Array} ideas - Array de ideas seleccionadas
   * @returns {Array} - Array de tags únicos
   */
  const getCombinedTags = (figures, ideas) => {
    const allTags = [
      ...figures.flatMap(f => f.tags || []),
      ...ideas.flatMap(i => i.tags || [])
    ];
    return [...new Set(allTags)];
  };

  return {
    generatePrompt,
    generateStyledPrompt,
    generatePromptVariations,
    getCombinedTags
  };
}

/* app global css */
@import url('https://fonts.googleapis.com/css2?family=Host+Grotesk:ital,wght@0,300..800;1,300..800&family=National+Park:wght@200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');

/* Variables CSS personalizadas - Nuevo sistema de colores */
:root {
  /* Colores principales - Paleta moderna y sofisticada */
  --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

  /* Gradientes de fondo */
  --bg-gradient-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --bg-gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.12);
  --glass-bg-strong: rgba(255, 255, 255, 0.15);

  /* Sombras modernas */
  --shadow-soft: 0 4px 24px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.08);
  --shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.12);
  --shadow-colored: 0 8px 32px rgba(99, 102, 241, 0.15);

  /* Bordes y espaciado */
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --border-radius-xl: 28px;

  /* Transiciones */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* Tipografía */
  --font-family-primary: "Host Grotesk", sans-serif;

  --font-family-display: "Host Grotesk", sans-serif;

  /* Colores de texto */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #f8fafc;
}

/* Tipografía base */
* {
  font-family: var(--font-family-primary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-display);
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

.body--dark h1,
.body--dark h2,
.body--dark h3,
.body--dark h4,
.body--dark h5,
.body--dark h6 {
  color: var(--text-inverse);
}

/* Clases de texto con gradiente */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.text-gradient-secondary {
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.text-gradient-accent {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

/* Efectos de cristal (glassmorphism) mejorados */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-soft);
}

.glass-card-strong {
  background: var(--glass-bg-strong);
  backdrop-filter: blur(32px);
  -webkit-backdrop-filter: blur(32px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
}

.glass-card-colored {
  background: rgba(99, 102, 241, 0.08);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(99, 102, 241, 0.12);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-colored);
}

/* Botones modernos mejorados */
.btn-modern {
  border-radius: var(--border-radius);
  font-weight: 600;
  letter-spacing: 0.025em;
  transition: var(--transition);
  box-shadow: var(--shadow-soft);
  font-family: var(--font-family-primary);
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-gradient {
  background: var(--primary-gradient);
  border: none;
  color: white;
}

.btn-gradient:hover {
  box-shadow: var(--shadow-colored);
}

.btn-gradient-secondary {
  background: var(--secondary-gradient);
  border: none;
  color: white;
}

.btn-gradient-accent {
  background: var(--accent-gradient);
  border: none;
  color: white;
}

.btn-minimal {
  background: transparent;
  border: 1px solid var(--glass-border);
  color: var(--text-secondary);
  backdrop-filter: blur(8px);
}

.btn-minimal:hover {
  background: var(--glass-bg);
  color: var(--text-primary);
}

/* Cards modernas */
.modern-card {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow-soft);
  transition: var(--transition);
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.modern-card-featured {
  background: var(--primary-gradient);
  color: white;
}

/* Animaciones suaves */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Espaciado moderno */
.section-padding {
  padding: 4rem 0;
}

.container-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Efectos hover modernos */
.hover-lift {
  transition: var(--transition);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-strong);
}

/* Modo oscuro mejorado */
.body--dark {
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
}

.body--dark .modern-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-gradient);
}

/* app global css */

/* Variables CSS personalizadas */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.2);
  --border-radius: 16px;
  --border-radius-lg: 24px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tipografía moderna */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Efectos de cristal (glassmorphism) */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-soft);
}

.glass-card-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
}

/* Botones modernos */
.btn-modern {
  border-radius: var(--border-radius);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: var(--transition);
  box-shadow: var(--shadow-soft);
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-gradient {
  background: var(--primary-gradient);
  border: none;
  color: white;
}

.btn-gradient-secondary {
  background: var(--secondary-gradient);
  border: none;
  color: white;
}

/* Cards modernas */
.modern-card {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow-soft);
  transition: var(--transition);
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.modern-card-featured {
  background: var(--primary-gradient);
  color: white;
}

/* Animaciones suaves */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Espaciado moderno */
.section-padding {
  padding: 4rem 0;
}

.container-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Efectos hover modernos */
.hover-lift {
  transition: var(--transition);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-strong);
}

/* Modo oscuro mejorado */
.body--dark {
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
}

.body--dark .modern-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-gradient);
}

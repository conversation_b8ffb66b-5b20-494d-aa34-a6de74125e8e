[{"id": "fig_1", "name": "<PERSON><PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["nihilismo", "voluntad", "modernidad"], "description": "Filósofo alemán conocido por su crítica a la moral tradicional y su concepto del superhombre"}, {"id": "fig_2", "name": "<PERSON><PERSON>", "category": "arte", "tags": ["dolor", "cuerpo", "identidad"], "description": "Pintora mexicana famosa por sus autorretratos y su exploración del dolor físico y emocional"}, {"id": "fig_3", "name": "<PERSON>", "category": "psicología", "tags": ["inconsciente", "arquetipos", "individuación"], "description": "Psiquiatra suizo fundador de la psicología analítica"}, {"id": "fig_4", "name": "Virginia Woolf", "category": "literatura", "tags": ["modernismo", "feminismo", "conciencia"], "description": "Escritora británica pionera del modernismo literario"}, {"id": "fig_5", "name": "<PERSON>", "category": "arte", "tags": ["renacimiento", "genio", "innovación"], "description": "Polímata del Renacimiento italiano, artista, inventor y científico"}, {"id": "fig_6", "name": "<PERSON>", "category": "filosofía", "tags": ["existencialismo", "feminismo", "libertad"], "description": "Filósofa francesa existencialista y teórica feminista"}, {"id": "fig_7", "name": "<PERSON>", "category": "arte", "tags": ["cubismo", "revolución", "creatividad"], "description": "Pintor español cofundador del cubismo"}, {"id": "fig_8", "name": "<PERSON><PERSON><PERSON>", "category": "psicología", "tags": ["psicoanálisis", "inconsciente", "represión"], "description": "Neurólogo austriaco fundador del psicoanálisis"}, {"id": "fig_9", "name": "<PERSON>", "category": "ciencia", "tags": ["radioactividad", "perseverancia", "pionera"], "description": "Física y química polaca, primera mujer en ganar un Premio Nobel"}, {"id": "fig_10", "name": "<PERSON>", "category": "ciencia", "tags": ["relatividad", "genio", "imaginación"], "description": "Físico teó<PERSON>, desarrollador de la teoría de la relatividad"}, {"id": "fig_11", "name": "Platón", "category": "filosofía", "tags": ["idealismo", "academia", "justicia"], "description": "Filósofo griego fundador de la Academia de Atenas"}, {"id": "fig_12", "name": "Aristóteles", "category": "filosofía", "tags": ["lógica", "ética", "política"], "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> griego <PERSON><PERSON><PERSON>, tutor <PERSON>"}, {"id": "fig_13", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["mayéutica", "conocimiento", "virtud"], "description": "Filósofo griego considerado fundador de la filosofía occidental"}, {"id": "fig_14", "name": "<PERSON><PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["crítica", "razón", "moral"], "description": "Filósofo alemán de la Ilustración, autor de la Crítica de la razón pura"}, {"id": "fig_15", "name": "<PERSON>", "category": "filosofía", "tags": ["racionalismo", "duda", "cogito"], "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> fran<PERSON>, padre de la filosofía moderna"}, {"id": "fig_16", "name": "<PERSON>", "category": "filosofía", "tags": ["empirismo", "escepticismo", "causalidad"], "description": "Filósofo escocés empirista de la Ilustración"}, {"id": "fig_17", "name": "<PERSON>", "category": "filosofía", "tags": ["utilitarismo", "libertad", "individualismo"], "description": "Filósofo y economista británico, defensor del liberalismo"}, {"id": "fig_18", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["existencialismo", "libertad", "angustia"], "description": "<PERSON>lósofo francés existencialista, Premio Nobel de Literatura"}, {"id": "fig_19", "name": "<PERSON>", "category": "filosofía", "tags": ["ser", "tiempo", "existencia"], "description": "<PERSON>l<PERSON><PERSON><PERSON>, influyente en el existencialismo y la hermenéutica"}, {"id": "fig_20", "name": "<PERSON>", "category": "filosofía", "tags": ["lenguaje", "lógica", "juegos"], "description": "Filósofo austríaco, revolucionó la filosofía del lenguaje"}, {"id": "fig_21", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["ética", "virtud", "educación"], "description": "Filósofo chino cuyas enseñanzas enfatizan la ética personal y gubernamental"}, {"id": "fig_22", "name": "Lao Tzu", "category": "filosofía", "tags": ["<PERSON><PERSON><PERSON><PERSON>", "naturaleza", "wu wei"], "description": "Filósofo chino legendario, fundador del taoísmo"}, {"id": "fig_23", "name": "Buda", "category": "filosofía", "tags": ["budismo", "iluminación", "sufrimiento"], "description": "Fundador del budismo, enseñó el camino hacia la iluminación"}, {"id": "fig_24", "name": "<PERSON><PERSON>", "category": "filosofía", "tags": ["hedonismo", "placer", "ataraxia"], "description": "Filósofo griego fundador del epicureísmo"}, {"id": "fig_25", "name": "<PERSON>", "category": "filosofía", "tags": ["estoicismo", "virtud", "emperador"], "description": "Emperador romano y filósofo estoico"}, {"id": "fig_26", "name": "Epicteto", "category": "filosofía", "tags": ["estoicismo", "libertad", "control"], "description": "Filósofo estoico griego, enseñó sobre el control interno"}, {"id": "fig_27", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["racionalismo", "ética", "determinismo"], "description": "Filósofo holandés racionalista del siglo XVII"}, {"id": "fig_28", "name": "<PERSON>", "category": "filosofía", "tags": ["empirismo", "liberalismo", "tabula rasa"], "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, influyente en el liberalismo político"}, {"id": "fig_29", "name": "<PERSON><PERSON>", "category": "filosofía", "tags": ["ilustración", "tolerancia", "razón"], "description": "<PERSON>lósof<PERSON> fran<PERSON> de la Ilustración, defensor de la tolerancia"}, {"id": "fig_30", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["contrato social", "naturaleza", "educación"], "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ran<PERSON>, teórico del contrato social"}, {"id": "fig_31", "name": "<PERSON>", "category": "literatura", "tags": ["teatro", "tragedia", "humanidad"], "description": "Dramaturgo inglés, considerado el mejor escritor en lengua inglesa"}, {"id": "fig_32", "name": "<PERSON>", "category": "literatura", "tags": ["novela", "realismo", "quijote"], "description": "Escrito<PERSON><PERSON>, autor <PERSON> la Mancha"}, {"id": "fig_33", "name": "<PERSON><PERSON><PERSON>", "category": "literatura", "tags": ["psicología", "existencialismo", "crimen"], "description": "<PERSON><PERSON> ruso, explorador de la psicología humana"}, {"id": "fig_34", "name": "<PERSON>", "category": "literatura", "tags": ["realismo", "guerra", "paz"], "description": "Escritor ruso, autor de Guerra y Paz"}, {"id": "fig_35", "name": "<PERSON>", "category": "literatura", "tags": ["modernismo", "stream", "ulises"], "description": "Escritor irlandés, innovador del modernismo literario"}, {"id": "fig_36", "name": "<PERSON>", "category": "literatura", "tags": ["memoria", "tiempo", "búsqueda"], "description": "Escritor francé<PERSON>, autor de En busca del tiempo perdido"}, {"id": "fig_37", "name": "<PERSON>", "category": "literatura", "tags": ["absurdo", "alienación", "metamorfosis"], "description": "Escritor checo, maestro de lo absurdo y la alienación"}, {"id": "fig_38", "name": "<PERSON>", "category": "literatura", "tags": ["laberintos", "infinito", "ficción"], "description": "Escritor argentino, maestro del cuento fantástico"}, {"id": "fig_39", "name": "<PERSON>", "category": "literatura", "tags": ["<PERSON>o m<PERSON>o", "soledad", "américa"], "description": "Escrito<PERSON><PERSON><PERSON><PERSON>, Pre<PERSON>, maestro del realismo mágico"}, {"id": "fig_40", "name": "<PERSON>", "category": "literatura", "tags": ["poesía", "amor", "política"], "description": "<PERSON><PERSON>, Premio Nobel de Literatura"}, {"id": "fig_41", "name": "<PERSON>", "category": "literatura", "tags": ["poesía", "muerte", "naturaleza"], "description": "<PERSON><PERSON>, innovadora de la poesía moderna"}, {"id": "fig_42", "name": "<PERSON>", "category": "literatura", "tags": ["verso libre", "democracia", "américa"], "description": "Poeta estadounidense, revolucionó la poesía con verso libre"}, {"id": "fig_43", "name": "<PERSON><PERSON>", "category": "literatura", "tags": ["mística", "amor", "sufismo"], "description": "<PERSON>a persa, maestro de la poesía mística sufí"}, {"id": "fig_44", "name": "<PERSON><PERSON><PERSON>", "category": "literatura", "tags": ["poesía", "ensayo", "méxico"], "description": "Poet<PERSON> y ensayista mexicano, Premio Nobel de Literatura"}, {"id": "fig_45", "name": "<PERSON>", "category": "literatura", "tags": ["identidad", "raza", "memoria"], "description": "Escritora estadounidense, Premio Nobel, exploradora de la identidad afroamericana"}, {"id": "fig_46", "name": "<PERSON>", "category": "ciencia", "tags": ["gravedad", "física", "matemá<PERSON><PERSON>"], "description": "Físico y matemático inglés, formuló las leyes del movimiento"}, {"id": "fig_47", "name": "<PERSON>", "category": "ciencia", "tags": ["evolución", "selección natural", "biología"], "description": "Naturalista inglés, desarrolló la teoría de la evolución"}, {"id": "fig_48", "name": "<PERSON>", "category": "ciencia", "tags": ["astronomía", "heliocentrismo", "telescopio"], "description": "Científico italiano, defensor del heliocentrismo"}, {"id": "fig_49", "name": "<PERSON>", "category": "ciencia", "tags": ["electricidad", "invención", "energía"], "description": "Inventor serbio-estadounidense, pionero de la electricidad"}, {"id": "fig_50", "name": "<PERSON>", "category": "ciencia", "tags": ["cosmología", "agujeros negros", "tiempo"], "description": "Físico teórico británico, estudioso de los agujeros negros"}, {"id": "fig_51", "name": "<PERSON><PERSON>", "category": "ciencia", "tags": ["ADN", "cristalografía", "biología"], "description": "Química británica, clave en el descubrimiento de la estructura del ADN"}, {"id": "fig_52", "name": "<PERSON>", "category": "ciencia", "tags": ["penicilina", "medicina", "antibióticos"], "description": "<PERSON><PERSON><PERSON><PERSON>, descubridor de la penicilina"}, {"id": "fig_53", "name": "<PERSON>", "category": "ciencia", "tags": ["microbiología", "vacunas", "pasteurización"], "description": "Químico francés, pionero de la microbiología"}, {"id": "fig_54", "name": "<PERSON>", "category": "ciencia", "tags": ["genética", "herencia", "guisantes"], "description": "Monje y científico austriaco, padre de la genética"}, {"id": "fig_55", "name": "<PERSON>", "category": "ciencia", "tags": ["computación", "algoritmos", "matemá<PERSON><PERSON>"], "description": "Matemática británica, primera programadora de la historia"}, {"id": "fig_56", "name": "<PERSON>", "category": "ciencia", "tags": ["computación", "inteligencia artificial", "código"], "description": "Matemático británico, padre de la computación moderna"}, {"id": "fig_57", "name": "<PERSON>", "category": "ciencia", "tags": ["matemá<PERSON><PERSON>", "NASA", "espacio"], "description": "Matemática estadounidense, clave en los programas espaciales de NASA"}, {"id": "fig_58", "name": "<PERSON><PERSON><PERSON>", "category": "ciencia", "tags": ["química", "tabla periódica", "elementos"], "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruso, creador de la tabla periódica de elementos"}, {"id": "fig_59", "name": "<PERSON>", "category": "ciencia", "tags": ["física cuántica", "energía", "constante"], "description": "Físico <PERSON>, fundador de la teoría cuántica"}, {"id": "fig_60", "name": "<PERSON><PERSON>", "category": "ciencia", "tags": ["<PERSON><PERSON><PERSON>", "física cuántica", "modelo"], "description": "Físico <PERSON>, desarrolló el modelo atómico de Bohr"}, {"id": "fig_61", "name": "<PERSON><PERSON>", "category": "arte", "tags": ["renacimiento", "escultura", "capilla sixtina"], "description": "Artista italiano del Renacimiento, c<PERSON><PERSON> <PERSON> David y la Capilla Sixtina"}, {"id": "fig_62", "name": "<PERSON>", "category": "arte", "tags": ["posimpresionismo", "color", "expresión"], "description": "Pintor <PERSON>, maestro del color y la expresión emocional"}, {"id": "fig_63", "name": "<PERSON>", "category": "arte", "tags": ["impresionismo", "luz", "naturaleza"], "description": "<PERSON><PERSON><PERSON>, fundador del impresionismo"}, {"id": "fig_64", "name": "Salvador Dalí", "category": "arte", "tags": ["surrealismo", "subconsciente", "tiempo"], "description": "Pintor español surrealista, maestro de lo onírico"}, {"id": "fig_65", "name": "Georgia O'Keeffe", "category": "arte", "tags": ["modernismo", "naturaleza", "abstracción"], "description": "Pintora estadounidense, pionera del modernismo americano"}, {"id": "fig_66", "name": "<PERSON>", "category": "arte", "tags": ["pop art", "consumo", "celebridad"], "description": "Artista estadounidense, líder del movimiento pop art"}, {"id": "fig_67", "name": "<PERSON>", "category": "arte", "tags": ["expresionismo abstracto", "acción", "gesto"], "description": "Pintor estado<PERSON>den<PERSON>, pionero del expresionismo abstracto"}, {"id": "fig_68", "name": "<PERSON><PERSON><PERSON>", "category": "arte", "tags": ["abstracción", "color", "espiritualidad"], "description": "<PERSON>nto<PERSON> ruso, pionero del arte abstracto"}, {"id": "fig_69", "name": "<PERSON>", "category": "arte", "tags": ["fauvis<PERSON>", "color", "simplicidad"], "description": "<PERSON><PERSON><PERSON> fran<PERSON>, maestro del color y líder del fauvismo"}, {"id": "fig_70", "name": "<PERSON>", "category": "arte", "tags": ["escultura", "expresión", "modernidad"], "description": "Escultor francé<PERSON>, revolucionó la escultura moderna"}, {"id": "fig_71", "name": "<PERSON>", "category": "música", "tags": ["clasicismo", "genio", "perfección"], "description": "Compositor austriaco, genio del período clásico"}, {"id": "fig_72", "name": "<PERSON>", "category": "música", "tags": ["romanticismo", "sinfonía", "revolución"], "description": "Compositor <PERSON><PERSON>, puente entre clasicismo y romanticismo"}, {"id": "fig_73", "name": "<PERSON>", "category": "música", "tags": ["barroco", "contrapunto", "matemática"], "description": "Compositor <PERSON><PERSON>, maestro del barroco y el contrapunto"}, {"id": "fig_74", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "música", "tags": ["romanticismo", "piano", "poesía"], "description": "Composi<PERSON>, poeta del piano"}, {"id": "fig_75", "name": "<PERSON>", "category": "música", "tags": ["modernismo", "ritmo", "revolución"], "description": "Compositor ruso, revolucionó la música del siglo XX"}, {"id": "fig_76", "name": "<PERSON>", "category": "música", "tags": ["jazz", "innovación", "improvisación"], "description": "Trompetista estadounidense, innovador del jazz"}, {"id": "fig_77", "name": "<PERSON>", "category": "música", "tags": ["jazz", "espiritualidad", "saxofón"], "description": "Saxofonista estadounidense, explorador espiritual del jazz"}, {"id": "fig_78", "name": "<PERSON>", "category": "música", "tags": ["folk", "protesta", "poesía"], "description": "Cantautor estadounidense, voz de una generación"}, {"id": "fig_79", "name": "<PERSON><PERSON>", "category": "música", "tags": ["soul", "gospel", "empoderamiento"], "description": "Cantante estado<PERSON>, reina del soul"}, {"id": "fig_80", "name": "<PERSON>", "category": "música", "tags": ["jazz", "blues", "dolor"], "description": "Cantante estado<PERSON>, voz única del jazz y blues"}, {"id": "fig_81", "name": "<PERSON><PERSON><PERSON><PERSON>", "category": "psicología", "tags": ["conductismo", "condicionamiento", "comportamiento"], "description": "Psicólogo estadounidense, líder del conductismo"}, {"id": "fig_82", "name": "<PERSON>", "category": "psicología", "tags": ["humanismo", "autorrealización", "jerarq<PERSON><PERSON>"], "description": "Psicólogo estadounidense, creador de la pirámide de necesidades"}, {"id": "fig_83", "name": "<PERSON>", "category": "psicología", "tags": ["desarrollo", "cognición", "niños"], "description": "Psicólogo suizo, estudioso del desarrollo cognitivo"}, {"id": "fig_84", "name": "<PERSON>", "category": "psicología", "tags": ["desarrollo", "identidad", "crisis"], "description": "Psicólogo alemán-estadounidense, teórico del desarrollo psicosocial"}, {"id": "fig_85", "name": "<PERSON>", "category": "psicología", "tags": ["sentido", "logoterapia", "supervivencia"], "description": "Psiquiatra austriaco, creador de la logoterapia"}, {"id": "fig_86", "name": "<PERSON>", "category": "psicología", "tags": ["pragmatismo", "conciencia", "religión"], "description": "Psicólogo estadounidense, padre de la psicología americana"}, {"id": "fig_87", "name": "<PERSON>", "category": "psicología", "tags": ["condicionamiento", "reflejo", "aprendizaje"], "description": "Fisiólogo ruso, descubridor del condicionamiento clásico"}, {"id": "fig_88", "name": "<PERSON>", "category": "psicología", "tags": ["psicología individual", "inferioridad", "superación"], "description": "Psiquiatra austriaco, fundador de la psicología individual"}, {"id": "fig_89", "name": "<PERSON>", "category": "psicología", "tags": ["psicoanálisis", "feminismo", "neurosis"], "description": "Psicoanalista alemana-estadounidense, crítica del psicoanálisis freudiano"}, {"id": "fig_90", "name": "<PERSON>", "category": "psicología", "tags": ["psicoanálisis infantil", "mecanismos defensa", "desarrollo"], "description": "Psicoanalista austriaca-británica, pionera del psicoanálisis infantil"}]
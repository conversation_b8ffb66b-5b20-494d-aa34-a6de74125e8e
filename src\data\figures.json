[{"id": "fig_11", "name": "<PERSON>", "category": "ciencia", "tags": ["gravedad", "física", "matemá<PERSON><PERSON>"], "description": "Físico y matemático inglés, conocido por formular las leyes del movimiento y la gravitación universal."}, {"id": "fig_12", "name": "<PERSON>", "category": "música", "tags": ["clasicismo", "genio", "ópera"], "description": "Compositor austr<PERSON><PERSON>, uno de los más grandes músicos en la historia occidental."}, {"id": "fig_13", "name": "<PERSON>", "category": "literatura", "tags": ["renacimiento", "teatro", "tragedia"], "description": "Dramaturgo inglés, ampliamente considerado el mejor escritor en lengua inglesa."}, {"id": "fig_14", "name": "<PERSON>", "category": "política", "tags": ["libertad", "<PERSON><PERSON><PERSON><PERSON>", "apartheid"], "description": "Político sudafricano, líder contra el apartheid y primer presidente negro de Sudáfrica."}, {"id": "fig_15", "name": "<PERSON><PERSON><PERSON>", "category": "filosofía", "tags": ["ética", "virtud", "educación"], "description": "Filósofo chino cuyas enseñanzas enfatizan la ética personal y gubernamental."}, {"id": "fig_16", "name": "<PERSON>", "category": "música", "tags": ["barroco", "contrapunto", "armonía"], "description": "Compositor <PERSON><PERSON>, maestro del contrapunto y del barroco musical."}, {"id": "fig_17", "name": "<PERSON>", "category": "ciencia", "tags": ["evolución", "selección natural", "biología"], "description": "Naturalista inglés famoso por su teoría de la evolución mediante selección natural."}, {"id": "fig_18", "name": "<PERSON><PERSON>", "category": "arte", "tags": ["renacimiento", "escultura", "pintura"], "description": "Artista italiano del Renacimiento, c<PERSON>or <PERSON> y pintor de la Capilla Sixtina."}, {"id": "fig_19", "name": "<PERSON>", "category": "música", "tags": ["romanticismo", "innovación", "sinfonía"], "description": "Compositor <PERSON>, figura crucial en la transición entre clasicismo y romanticismo musical."}, {"id": "fig_20", "name": "<PERSON>", "category": "ciencia", "tags": ["astronomía", "heliocentrismo", "innovación"], "description": "Científico italiano clave en la Revolución Científica, defensor del heliocentrismo."}, {"id": "fig_21", "name": "<PERSON>", "category": "literatura", "tags": ["poesía", "amor", "política"], "description": "<PERSON><PERSON>, premio Nobel de Literatura conocido por su poesía apasionada y comprometida."}, {"id": "fig_22", "name": "Thomas <PERSON>", "category": "ciencia", "tags": ["inventor", "electricidad", "innovación"], "description": "Inventor estadounidense, conocido por inventar la bombilla eléctrica."}, {"id": "fig_23", "name": "<PERSON>", "category": "arte", "tags": ["escultura", "modernidad", "expresión"], "description": "<PERSON>s<PERSON><PERSON><PERSON> fran<PERSON>, precursor de la escultura moderna, famoso por 'El pensador'."}, {"id": "fig_24", "name": "<PERSON>", "category": "literatura", "tags": ["absurdo", "existencialismo", "alienación"], "description": "Escritor checo cuya obra explora temas existencialistas y surrealistas."}, {"id": "fig_25", "name": "<PERSON>po<PERSON>", "category": "ciencia", "tags": ["medicina", "ética", "antigüedad"], "description": "Médico griego considerado el padre de la medicina occidental."}, {"id": "fig_26", "name": "<PERSON><PERSON>", "category": "literatura", "tags": ["mística", "poesía", "amor"], "description": "Poeta persa, uno de los mayores exponentes de la poesía sufí."}, {"id": "fig_27", "name": "<PERSON> Jr.", "category": "política", "tags": ["derechos civiles", "no violencia", "<PERSON><PERSON><PERSON><PERSON>"], "description": "Líder estadounidense del movimiento por los derechos civiles."}, {"id": "fig_28", "name": "<PERSON>", "category": "arte", "tags": ["posimpresionismo", "color", "expresión"], "description": "Pintor neerlandés conocido por su impacto en el arte moderno con obras como 'La noche estrellada'."}, {"id": "fig_29", "name": "<PERSON>", "category": "ciencia", "tags": ["computación", "matemá<PERSON><PERSON>", "pionera"], "description": "Matemática británica, considerada la primera programadora de la historia."}, {"id": "fig_30", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "ciencia", "tags": ["álgebra", "matemá<PERSON><PERSON>", "algoritmo"], "description": "Matem<PERSON><PERSON>o persa, conocido por sentar las bases del álgebra y los algoritmos modernos."}]
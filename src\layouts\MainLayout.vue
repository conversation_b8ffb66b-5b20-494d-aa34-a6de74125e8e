<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title>
          🧠 Sabiedumbre
        </q-toolbar-title>

        <q-btn
          flat
          round
          icon="dark_mode"
          @click="$q.dark.toggle()"
          :title="$q.dark.isActive ? 'Modo claro' : 'Modo oscuro'"
        />
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
    >
      <q-list>
        <q-item-label header>
          🧠 Sabiedumbre
        </q-item-label>

        <q-item
          v-for="link in navigationLinks"
          :key="link.title"
          :to="link.to"
          clickable
          v-ripple
        >
          <q-item-section avatar>
            <q-icon :name="link.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ link.title }}</q-item-label>
            <q-item-label caption>{{ link.caption }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-separator class="q-my-md" />

        <q-item-label header>
          Enlaces Útiles
        </q-item-label>

        <q-item
          v-for="link in externalLinks"
          :key="link.title"
          :href="link.link"
          target="_blank"
          clickable
          v-ripple
        >
          <q-item-section avatar>
            <q-icon :name="link.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ link.title }}</q-item-label>
            <q-item-label caption>{{ link.caption }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'MainLayout'
})

const navigationLinks = [
  {
    title: 'Inicio',
    caption: 'Bienvenida y explicación',
    icon: 'home',
    to: '/'
  },
  {
    title: 'Crear Mezcla',
    caption: 'Genera textos creativos',
    icon: 'auto_awesome',
    to: '/mezcla'
  },
  {
    title: 'Explorar',
    caption: 'Ver todas las figuras e ideas',
    icon: 'explore',
    to: '/explorar'
  }
]

const externalLinks = [
  {
    title: 'ChatGPT',
    caption: 'Usa tus prompts aquí',
    icon: 'chat',
    link: 'https://chat.openai.com'
  },
  {
    title: 'DeepSeek',
    caption: 'Alternativa de IA',
    icon: 'psychology',
    link: 'https://chat.deepseek.com'
  },
  {
    title: 'GitHub',
    caption: 'Código del proyecto',
    icon: 'code',
    link: 'https://github.com/bv-bjj/sabiedumbre'
  }
]

const leftDrawerOpen = ref(false)

function toggleLeftDrawer () {
  leftDrawerOpen.value = !leftDrawerOpen.value
}
</script>

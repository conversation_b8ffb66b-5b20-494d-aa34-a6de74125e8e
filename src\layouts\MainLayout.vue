<template>
  <q-layout view="lHh Lpr lFf" class="modern-layout">
    <q-header class="modern-header" height-hint="80">
      <q-toolbar class="modern-toolbar">
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
          class="menu-btn"
          size="lg"
        />

        <q-toolbar-title class="modern-title">
          <span class="brain-icon">🧠</span>
          <span class="title-text text-gradient">Sabiedumbre</span>
        </q-toolbar-title>

        <div class="header-actions">
          <q-btn
            flat
            round
            :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'"
            @click="$q.dark.toggle()"
            :title="$q.dark.isActive ? 'Modo claro' : 'Modo oscuro'"
            class="theme-btn"
            size="md"
          />
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      class="modern-drawer"
      :width="280"
    >
      <div class="drawer-content">
        <div class="drawer-header">
          <div class="drawer-title">
            <span class="brain-icon">🧠</span>
            <span class="text-gradient">Sabiedumbre</span>
          </div>
          <p class="drawer-subtitle">Creatividad sin límites</p>
        </div>

        <q-list class="navigation-list">
          <q-item-label header class="nav-header">
            Navegación
          </q-item-label>

          <q-item
            v-for="link in navigationLinks"
            :key="link.title"
            :to="link.to"
            clickable
            v-ripple
            class="nav-item"
            active-class="nav-item-active"
          >
            <q-item-section avatar>
              <q-icon :name="link.icon" size="1.2rem" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="nav-label">{{ link.title }}</q-item-label>
              <q-item-label caption class="nav-caption">{{ link.caption }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-separator class="nav-separator" />

          <q-item-label header class="nav-header">
            Enlaces Útiles
          </q-item-label>

          <q-item
            v-for="link in externalLinks"
            :key="link.title"
            :href="link.link"
            target="_blank"
            clickable
            v-ripple
            class="nav-item external-link"
          >
            <q-item-section avatar>
              <q-icon :name="link.icon" size="1.2rem" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="nav-label">{{ link.title }}</q-item-label>
              <q-item-label caption class="nav-caption">{{ link.caption }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon name="open_in_new" size="1rem" />
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-drawer>

    <q-page-container class="modern-page-container">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'MainLayout'
})

const navigationLinks = [
  {
    title: 'Inicio',
    caption: 'Bienvenida y explicación',
    icon: 'home',
    to: '/'
  },
  {
    title: 'Crear Mezcla',
    caption: 'Genera textos creativos',
    icon: 'auto_awesome',
    to: '/mezcla'
  },
  {
    title: 'Explorar',
    caption: 'Ver todas las figuras e ideas',
    icon: 'explore',
    to: '/explorar'
  }
]

const externalLinks = [
  {
    title: 'ChatGPT',
    caption: 'Usa tus prompts aquí',
    icon: 'chat',
    link: 'https://chat.openai.com'
  },
  {
    title: 'DeepSeek',
    caption: 'Alternativa de IA',
    icon: 'psychology',
    link: 'https://chat.deepseek.com'
  },
  {
    title: 'GitHub',
    caption: 'Código del proyecto',
    icon: 'code',
    link: 'https://github.com/bv-bjj/sabiedumbre'
  }
]

const leftDrawerOpen = ref(false)

function toggleLeftDrawer () {
  leftDrawerOpen.value = !leftDrawerOpen.value
}
</script>

<style scoped>
.modern-layout {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.body--dark .modern-layout {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Header */
.modern-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.body--dark .modern-header {
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modern-toolbar {
  height: 80px;
  padding: 0 2rem;
}

.menu-btn {
  color: #64748b;
  transition: var(--transition);
}

.menu-btn:hover {
  color: #3b82f6;
  transform: scale(1.1);
}

.modern-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.8rem;
  font-weight: 800;
}

.brain-icon {
  font-size: 2rem;
  animation: pulse 3s infinite;
}

.title-text {
  letter-spacing: -1px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-btn {
  color: #64748b;
  transition: var(--transition);
}

.theme-btn:hover {
  color: #f59e0b;
  transform: scale(1.1);
}

/* Drawer */
.modern-drawer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.body--dark .modern-drawer {
  background: rgba(0, 0, 0, 0.4);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  padding: 2rem 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.5);
}

.body--dark .drawer-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.drawer-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.drawer-subtitle {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
  font-style: italic;
}

.navigation-list {
  flex: 1;
  padding: 1rem 0;
}

.nav-header {
  color: #64748b;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 1rem 1.5rem 0.5rem;
}

.nav-item {
  margin: 0.25rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  color: #475569;
}

.body--dark .nav-item {
  color: #cbd5e1;
}

.nav-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.nav-item-active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-soft);
}

.nav-item-active:hover {
  background: var(--primary-gradient);
  transform: translateX(4px);
}

.nav-label {
  font-weight: 600;
  font-size: 0.95rem;
}

.nav-caption {
  font-size: 0.8rem;
  opacity: 0.7;
}

.external-link {
  opacity: 0.8;
}

.external-link:hover {
  opacity: 1;
  background: rgba(16, 185, 129, 0.1);
}

.nav-separator {
  margin: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.1);
}

.body--dark .nav-separator {
  background: rgba(255, 255, 255, 0.1);
}

/* Page Container */
.modern-page-container {
  background: transparent;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .modern-toolbar {
    padding: 0 1rem;
  }

  .modern-title {
    font-size: 1.5rem;
  }

  .brain-icon {
    font-size: 1.5rem;
  }
}
</style>

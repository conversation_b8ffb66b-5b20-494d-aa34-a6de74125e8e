<template>
  <q-layout view="lHh Lpr lFf" class="modern-layout">
    <q-header class="modern-header" height-hint="80">
      <q-toolbar class="modern-toolbar flex justify-between">
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
          class="menu-btn"
          size="lg"
        />

  
        <div class="header-actions">
          <q-btn
            flat
            round
            :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'"
            @click="toggleDarkMode"
            :title="$q.dark.isActive ? 'Modo claro' : 'Modo oscuro'"
            class="theme-btn"
            size="md"
          />
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      class="minimal-drawer"
      :width="240"
    >
      <div class="drawer-content">
        <!-- Logo minimalista -->
        <div class="drawer-logo">
          <div class="logo-container">
            <span class="logo-icon">🧠</span>
            <span class="logo-text text-gradient">Sabiedumbre</span>
          </div>
        </div>

        <!-- Navegación principal -->
        <nav class="main-nav">
          <q-item
            v-for="link in navigationLinks"
            :key="link.title"
            :to="link.to"
            clickable
            class="nav-item"
            active-class="nav-item-active"
          >
            <q-item-section avatar class="nav-icon">
              <q-icon :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="nav-label">{{ link.title }}</q-item-label>
            </q-item-section>
          </q-item>
        </nav>

        <!-- Separador sutil -->
        <div class="nav-divider"></div>

        <!-- Enlaces externos minimalistas -->
        <nav class="external-nav">
          <q-item
            v-for="link in externalLinks"
            :key="link.title"
            :href="link.link"
            target="_blank"
            clickable
            class="nav-item external-item"
          >
            <q-item-section avatar class="nav-icon">
              <q-icon :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="nav-label">{{ link.title }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon name="open_in_new" size="0.8rem" class="external-icon" />
            </q-item-section>
          </q-item>
        </nav>

        <!-- Footer minimalista -->
        <div class="drawer-footer">
          <div class="version-info">v1.0.0</div>
        </div>
      </div>
    </q-drawer>

    <q-page-container class="modern-page-container">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'

defineOptions({
  name: 'MainLayout'
})

const $q = useQuasar()

const navigationLinks = [
  {
    title: 'Inicio',
    icon: 'home',
    to: '/'
  },
  {
    title: 'Crear Mezcla',
    icon: 'auto_awesome',
    to: '/mezcla'
  },
  {
    title: 'Explorar',
    icon: 'explore',
    to: '/explorar'
  },
  // {
  //   title: 'Perfil',
  //   icon: 'person',
  //   to: '/perfil'
  // }
]

const externalLinks = [
  {
    title: 'ChatGPT',
    caption: 'Usa tus prompts aquí',
    icon: 'chat',
    link: 'https://chat.openai.com'
  },
  {
    title: 'DeepSeek',
    caption: 'Alternativa de IA',
    icon: 'psychology',
    link: 'https://chat.deepseek.com'
  },
  {
    title: 'GitHub',
    caption: 'Código del proyecto',
    icon: 'code',
    link: 'https://github.com/bv-bjj/sabiedumbre'
  }
]

const leftDrawerOpen = ref(false)

function toggleLeftDrawer () {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

// Persistencia del modo oscuro
function toggleDarkMode() {
  $q.dark.toggle()
  localStorage.setItem('sabiedumbre-dark-mode', $q.dark.isActive.toString())
}

// Cargar modo oscuro al iniciar
onMounted(() => {
  const savedDarkMode = localStorage.getItem('sabiedumbre-dark-mode')
  if (savedDarkMode !== null) {
    $q.dark.set(savedDarkMode === 'true')
  }
})
</script>

<style scoped>
.modern-layout {
  background: var(--bg-gradient-light);
  min-height: 100vh;
}

.body--dark .modern-layout {
  background: var(--bg-gradient-dark);
}

/* Header */
.modern-header {
  background: var(--glass-bg-strong);
  backdrop-filter: blur(24px);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-soft);
}

.body--dark .modern-header {
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: var(--shadow-medium);
}

.modern-toolbar {
  height: 80px;
  padding: 0 2rem;
}

.menu-btn {
  color: #64748b;
  transition: var(--transition);
}

.menu-btn:hover {
  color: #3b82f6;
  transform: scale(1.1);
}

.modern-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.8rem;
  font-weight: 800;
}

.brain-icon {
  font-size: 2rem;
  animation: pulse 3s infinite;
}

.title-text {
  letter-spacing: -1px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-btn {
  color: #64748b;
  transition: var(--transition);
}

.theme-btn:hover {
  color: #f59e0b;
  transform: scale(1.1);
}

/* Drawer minimalista */
.minimal-drawer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.body--dark .minimal-drawer {
  background: rgba(15, 23, 42, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem 0;
}

/* Logo minimalista */
.drawer-logo {
  padding: 0 1.5rem 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 800;
  letter-spacing: -0.025em;
}

/* Navegación principal */
.main-nav {
  flex: 1;
  padding: 0 1rem;
}

.nav-item {
  margin-bottom: 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition-fast);
  color: var(--text-secondary);
  min-height: 48px;
  position: relative;
}

.body--dark .nav-item {
  color: #cbd5e1;
}

.nav-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #1e293b;
}

.body--dark .nav-item:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #f1f5f9;
}

.nav-item-active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-colored);
}

.nav-item-active::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: white;
  border-radius: 2px;
}

.nav-icon {
  min-width: 40px;
}

.nav-label {
  font-weight: 500;
  font-size: 0.9rem;
  letter-spacing: 0.01em;
}

/* Divisor sutil */
.nav-divider {
  height: 1px;
  background: var(--glass-border);
  margin: 1.5rem 1rem;
}

/* Enlaces externos */
.external-nav {
  padding: 0 1rem;
}

.external-item {
  opacity: 0.7;
  min-height: 40px;
}

.external-item:hover {
  opacity: 1;
  background: var(--glass-bg);
}

.external-icon {
  opacity: 0.5;
}

/* Footer */
.drawer-footer {
  padding: 1rem 1.5rem;
  text-align: center;
}

.version-info {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* Page Container */
.modern-page-container {
  background: transparent;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .modern-toolbar {
    padding: 0 1rem;
  }

  .modern-title {
    font-size: 1.5rem;
  }

  .brain-icon {
    font-size: 1.5rem;
  }
}
</style>

<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="col-12">
        <!-- Header -->
        <div class="text-center q-mb-xl">
          <h3 class="text-h3 q-mb-md">🔍 Explorar Contenido</h3>
          <p class="text-subtitle1 text-grey-7">
            Descubre todas las figuras y conceptos disponibles para tus mezclas creativas
          </p>
        </div>

        <!-- Tabs -->
        <q-tabs
          v-model="activeTab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
        >
          <q-tab name="figures" icon="people" label="Figuras" />
          <q-tab name="ideas" icon="lightbulb" label="Ideas" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="activeTab" animated>
          <!-- Figuras Panel -->
          <q-tab-panel name="figures">
            <div class="q-mb-md">
              <q-input
                v-model="figureSearch"
                placeholder="Buscar figuras..."
                outlined
                dense
                clearable
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </div>

            <!-- Filtros por categoría -->
            <div class="q-mb-md">
              <q-chip
                v-for="category in figureCategories"
                :key="category"
                :label="category"
                clickable
                :color="selectedFigureCategory === category ? 'primary' : 'grey-3'"
                :text-color="selectedFigureCategory === category ? 'white' : 'grey-7'"
                @click="toggleFigureCategory(category)"
              />
              <q-chip
                label="Todas"
                clickable
                :color="selectedFigureCategory === null ? 'primary' : 'grey-3'"
                :text-color="selectedFigureCategory === null ? 'white' : 'grey-7'"
                @click="selectedFigureCategory = null"
              />
            </div>

            <!-- Grid de figuras -->
            <div class="row q-gutter-md">
              <div
                v-for="figure in filteredFigures"
                :key="figure.id"
                class="col-12 col-sm-6 col-md-4 col-lg-3"
              >
                <IdeaSelect
                  :item="figure"
                  type="figure"
                  :selected="selectedFigures.includes(figure)"
                  @toggle="toggleFigureSelection"
                />
              </div>
            </div>
          </q-tab-panel>

          <!-- Ideas Panel -->
          <q-tab-panel name="ideas">
            <div class="q-mb-md">
              <q-input
                v-model="ideaSearch"
                placeholder="Buscar ideas..."
                outlined
                dense
                clearable
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </div>

            <!-- Filtros por dominio -->
            <div class="q-mb-md">
              <q-chip
                v-for="domain in ideaDomains"
                :key="domain"
                :label="domain"
                clickable
                :color="selectedIdeaDomain === domain ? 'secondary' : 'grey-3'"
                :text-color="selectedIdeaDomain === domain ? 'white' : 'grey-7'"
                @click="toggleIdeaDomain(domain)"
              />
              <q-chip
                label="Todas"
                clickable
                :color="selectedIdeaDomain === null ? 'secondary' : 'grey-3'"
                :text-color="selectedIdeaDomain === null ? 'white' : 'grey-7'"
                @click="selectedIdeaDomain = null"
              />
            </div>

            <!-- Grid de ideas -->
            <div class="row q-gutter-md">
              <div
                v-for="idea in filteredIdeas"
                :key="idea.id"
                class="col-12 col-sm-6 col-md-4 col-lg-3"
              >
                <IdeaSelect
                  :item="idea"
                  type="idea"
                  :selected="selectedIdeas.includes(idea)"
                  @toggle="toggleIdeaSelection"
                />
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>

        <!-- Floating Action Button -->
        <q-page-sticky position="bottom-right" :offset="[18, 18]">
          <q-btn
            v-if="selectedFigures.length > 0 || selectedIdeas.length > 0"
            fab
            icon="auto_awesome"
            color="primary"
            @click="createMezcla"
          >
            <q-badge
              v-if="totalSelected > 0"
              color="red"
              floating
              :label="totalSelected"
            />
          </q-btn>
        </q-page-sticky>
      </div>
    </div>

    <!-- Modal de Mezcla -->
    <ModalMezcla
      v-model="showMezclaModal"
      :figures="figures"
      :ideas="ideas"
      @prompt-generated="onPromptGenerated"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import IdeaSelect from '../components/IdeaSelect.vue'
import ModalMezcla from '../components/ModalMezcla.vue'
import figuresData from '../data/figures.json'
import ideasData from '../data/ideas.json'

// Composables
const $q = useQuasar()
const router = useRouter()

// Reactive data
const activeTab = ref('figures')
const figures = ref([])
const ideas = ref([])
const selectedFigures = ref([])
const selectedIdeas = ref([])
const figureSearch = ref('')
const ideaSearch = ref('')
const selectedFigureCategory = ref(null)
const selectedIdeaDomain = ref(null)
const showMezclaModal = ref(false)

// Computed
const figureCategories = computed(() => {
  return [...new Set(figures.value.map(f => f.category))]
})

const ideaDomains = computed(() => {
  return [...new Set(ideas.value.map(i => i.domain))]
})

const filteredFigures = computed(() => {
  let filtered = figures.value

  // Filtrar por búsqueda
  if (figureSearch.value) {
    const search = figureSearch.value.toLowerCase()
    filtered = filtered.filter(f => 
      f.name.toLowerCase().includes(search) ||
      f.description.toLowerCase().includes(search) ||
      f.tags.some(tag => tag.toLowerCase().includes(search))
    )
  }

  // Filtrar por categoría
  if (selectedFigureCategory.value) {
    filtered = filtered.filter(f => f.category === selectedFigureCategory.value)
  }

  return filtered
})

const filteredIdeas = computed(() => {
  let filtered = ideas.value

  // Filtrar por búsqueda
  if (ideaSearch.value) {
    const search = ideaSearch.value.toLowerCase()
    filtered = filtered.filter(i => 
      i.name.toLowerCase().includes(search) ||
      i.description.toLowerCase().includes(search) ||
      i.tags.some(tag => tag.toLowerCase().includes(search))
    )
  }

  // Filtrar por dominio
  if (selectedIdeaDomain.value) {
    filtered = filtered.filter(i => i.domain === selectedIdeaDomain.value)
  }

  return filtered
})

const totalSelected = computed(() => {
  return selectedFigures.value.length + selectedIdeas.value.length
})

// Methods
const loadData = () => {
  figures.value = figuresData
  ideas.value = ideasData
}

const toggleFigureCategory = (category) => {
  selectedFigureCategory.value = selectedFigureCategory.value === category ? null : category
}

const toggleIdeaDomain = (domain) => {
  selectedIdeaDomain.value = selectedIdeaDomain.value === domain ? null : domain
}

const toggleFigureSelection = (figure) => {
  const index = selectedFigures.value.findIndex(f => f.id === figure.id)
  if (index > -1) {
    selectedFigures.value.splice(index, 1)
  } else {
    if (selectedFigures.value.length >= 2) {
      $q.notify({
        type: 'warning',
        message: 'Solo puedes seleccionar máximo 2 figuras'
      })
      return
    }
    selectedFigures.value.push(figure)
  }
}

const toggleIdeaSelection = (idea) => {
  const index = selectedIdeas.value.findIndex(i => i.id === idea.id)
  if (index > -1) {
    selectedIdeas.value.splice(index, 1)
  } else {
    if (selectedIdeas.value.length >= 2) {
      $q.notify({
        type: 'warning',
        message: 'Solo puedes seleccionar máximo 2 ideas'
      })
      return
    }
    selectedIdeas.value.push(idea)
  }
}

const createMezcla = () => {
  if (selectedFigures.value.length === 0 && selectedIdeas.value.length === 0) {
    $q.notify({
      type: 'warning',
      message: 'Selecciona al menos una figura o una idea'
    })
    return
  }
  
  showMezclaModal.value = true
}

const onPromptGenerated = (promptData) => {
  $q.notify({
    type: 'positive',
    message: 'Prompt generado exitosamente',
    icon: 'auto_awesome'
  })
  
  showMezclaModal.value = false
  
  // Opcional: redirigir a la página de mezcla
  router.push('/mezcla')
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.q-tab-panel {
  padding: 16px 0;
}
</style>

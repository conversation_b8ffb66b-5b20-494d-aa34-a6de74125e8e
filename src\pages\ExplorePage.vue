<template>
  <q-page class="modern-explore-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container-modern">
        <div class="hero-content fade-in">
          <div class="hero-title">
            <span class="explore-icon">🔍</span>
            <h1 class="text-gradient">Explorar Contenido</h1>
          </div>
          <p class="hero-subtitle">
            Descubre todas las figuras y conceptos disponibles para tus mezclas creativas
          </p>

          <!-- Selection Summary -->
          <div v-if="totalSelected > 0" class="selection-summary glass-card">
            <div class="summary-content">
              <q-icon name="check_circle" color="primary" size="1.5rem" />
              <span class="summary-text">
                {{ selectedFigures.length }} figura{{ selectedFigures.length !== 1 ? 's' : '' }} y
                {{ selectedIdeas.length }} idea{{ selectedIdeas.length !== 1 ? 's' : '' }} seleccionada{{ totalSelected !== 1 ? 's' : '' }}
              </span>
              <q-btn
                class="btn-modern btn-gradient"
                icon="auto_awesome"
                label="Crear Mezcla"
                @click="createMezcla"
                no-caps
                size="md"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Navigation Tabs -->
    <section class="tabs-section">
      <div class="container-modern">
        <div class="modern-tabs">
          <div
            class="tab-item"
            :class="{ 'active': activeTab === 'figures' }"
            @click="activeTab = 'figures'"
          >
            <q-icon name="people" size="1.5rem" />
            <span>Figuras Históricas</span>
            <div class="tab-count">{{ figures.length }}</div>
          </div>
          <div
            class="tab-item"
            :class="{ 'active': activeTab === 'ideas' }"
            @click="activeTab = 'ideas'"
          >
            <q-icon name="lightbulb" size="1.5rem" />
            <span>Conceptos e Ideas</span>
            <div class="tab-count">{{ ideas.length }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
      <div class="container-modern">
        <!-- Figuras Panel -->
        <div v-if="activeTab === 'figures'" class="content-panel">
          <!-- Search and Filters -->
          <div class="filters-section">
            <div class="search-container">
              <q-input
                v-model="figureSearch"
                placeholder="Buscar figuras históricas..."
                class="modern-search"
                outlined
                clearable
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </div>

            <!-- Category Filters -->
            <div class="category-filters">
              <h4>Filtrar por categoría:</h4>
              <div class="filter-chips">
                <q-chip
                  label="Todas"
                  clickable
                  :color="selectedFigureCategory === null ? 'primary' : 'grey-3'"
                  :text-color="selectedFigureCategory === null ? 'white' : 'grey-7'"
                  @click="selectedFigureCategory = null"
                  class="filter-chip"
                />
                <q-chip
                  v-for="category in figureCategories"
                  :key="category"
                  :label="category"
                  clickable
                  :color="selectedFigureCategory === category ? 'primary' : 'grey-3'"
                  :text-color="selectedFigureCategory === category ? 'white' : 'grey-7'"
                  @click="toggleFigureCategory(category)"
                  class="filter-chip"
                />
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="loading" class="loading-state text-center q-pa-xl">
            <q-spinner-dots size="3rem" color="primary" />
            <p class="text-grey-6 q-mt-md">Cargando figuras...</p>
          </div>

          <!-- Results -->
          <div v-else>
            <!-- Results Count -->
            <div class="results-info">
              <span class="results-count">{{ filteredFigures.length }} figura{{ filteredFigures.length !== 1 ? 's' : '' }} encontrada{{ filteredFigures.length !== 1 ? 's' : '' }}</span>
            </div>

            <!-- Grid de figuras -->
            <div class="items-grid">
              <IdeaSelect
                v-for="figure in filteredFigures"
                :key="figure.id"
                :item="figure"
                type="figure"
                :selected="selectedFigures.some(f => f.id === figure.id)"
                @toggle="toggleFigureSelection"
              />
            </div>
          </div>
        </div>

        <!-- Ideas Panel -->
        <div v-if="activeTab === 'ideas'" class="content-panel">
          <!-- Search and Filters -->
          <div class="filters-section">
            <div class="search-container">
              <q-input
                v-model="ideaSearch"
                placeholder="Buscar conceptos e ideas..."
                class="modern-search"
                outlined
                clearable
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </div>

            <!-- Domain Filters -->
            <div class="category-filters">
              <h4>Filtrar por dominio:</h4>
              <div class="filter-chips">
                <q-chip
                  label="Todas"
                  clickable
                  :color="selectedIdeaDomain === null ? 'secondary' : 'grey-3'"
                  :text-color="selectedIdeaDomain === null ? 'white' : 'grey-7'"
                  @click="selectedIdeaDomain = null"
                  class="filter-chip"
                />
                <q-chip
                  v-for="domain in ideaDomains"
                  :key="domain"
                  :label="domain"
                  clickable
                  :color="selectedIdeaDomain === domain ? 'secondary' : 'grey-3'"
                  :text-color="selectedIdeaDomain === domain ? 'white' : 'grey-7'"
                  @click="toggleIdeaDomain(domain)"
                  class="filter-chip"
                />
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="loading" class="loading-state text-center q-pa-xl">
            <q-spinner-dots size="3rem" color="secondary" />
            <p class="text-grey-6 q-mt-md">Cargando ideas...</p>
          </div>

          <!-- Results -->
          <div v-else>
            <!-- Results Count -->
            <div class="results-info">
              <span class="results-count">{{ filteredIdeas.length }} idea{{ filteredIdeas.length !== 1 ? 's' : '' }} encontrada{{ filteredIdeas.length !== 1 ? 's' : '' }}</span>
            </div>

            <!-- Grid de ideas -->
            <div class="items-grid">
              <IdeaSelect
                v-for="idea in filteredIdeas"
                :key="idea.id"
                :item="idea"
                type="idea"
                :selected="selectedIdeas.some(i => i.id === idea.id)"
                @toggle="toggleIdeaSelection"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Modal de Mezcla -->
    <ModalMezcla
      v-model="showMezclaModal"
      :figures="figures"
      :ideas="ideas"
      @prompt-generated="onPromptGenerated"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import IdeaSelect from '../components/IdeaSelect.vue'
import ModalMezcla from '../components/ModalMezcla.vue'
import { useAPI } from '../composables/useAPI'

// Composables
const $q = useQuasar()
const router = useRouter()
const {
  figures,
  ideas,
  figureCategories,
  ideaDomains,
  loading,
  initializeData,
  searchFigures,
  searchIdeas
} = useAPI()

// Reactive data
const activeTab = ref('figures')
const selectedFigures = ref([])
const selectedIdeas = ref([])
const figureSearch = ref('')
const ideaSearch = ref('')
const selectedFigureCategory = ref(null)
const selectedIdeaDomain = ref(null)
const showMezclaModal = ref(false)

// Computed (using API computed values)

const filteredFigures = computed(() => {
  let filtered = figures.value

  // Filtrar por búsqueda
  if (figureSearch.value) {
    const search = figureSearch.value.toLowerCase()
    filtered = filtered.filter(f => 
      f.name.toLowerCase().includes(search) ||
      f.description.toLowerCase().includes(search) ||
      f.tags.some(tag => tag.toLowerCase().includes(search))
    )
  }

  // Filtrar por categoría
  if (selectedFigureCategory.value) {
    filtered = filtered.filter(f => f.category === selectedFigureCategory.value)
  }

  return filtered
})

const filteredIdeas = computed(() => {
  let filtered = ideas.value

  // Filtrar por búsqueda
  if (ideaSearch.value) {
    const search = ideaSearch.value.toLowerCase()
    filtered = filtered.filter(i => 
      i.name.toLowerCase().includes(search) ||
      i.description.toLowerCase().includes(search) ||
      i.tags.some(tag => tag.toLowerCase().includes(search))
    )
  }

  // Filtrar por dominio
  if (selectedIdeaDomain.value) {
    filtered = filtered.filter(i => i.domain === selectedIdeaDomain.value)
  }

  return filtered
})

const totalSelected = computed(() => {
  return selectedFigures.value.length + selectedIdeas.value.length
})

// Methods
const loadData = () => {
  figures.value = figuresData
  ideas.value = ideasData
}

const toggleFigureCategory = (category) => {
  selectedFigureCategory.value = selectedFigureCategory.value === category ? null : category
}

const toggleIdeaDomain = (domain) => {
  selectedIdeaDomain.value = selectedIdeaDomain.value === domain ? null : domain
}

const toggleFigureSelection = (figure) => {
  const index = selectedFigures.value.findIndex(f => f.id === figure.id)
  if (index > -1) {
    selectedFigures.value.splice(index, 1)
  } else {
    if (selectedFigures.value.length >= 2) {
      $q.notify({
        type: 'warning',
        message: 'Solo puedes seleccionar máximo 2 figuras'
      })
      return
    }
    selectedFigures.value.push(figure)
  }
}

const toggleIdeaSelection = (idea) => {
  const index = selectedIdeas.value.findIndex(i => i.id === idea.id)
  if (index > -1) {
    selectedIdeas.value.splice(index, 1)
  } else {
    if (selectedIdeas.value.length >= 2) {
      $q.notify({
        type: 'warning',
        message: 'Solo puedes seleccionar máximo 2 ideas'
      })
      return
    }
    selectedIdeas.value.push(idea)
  }
}

const createMezcla = () => {
  if (selectedFigures.value.length === 0 && selectedIdeas.value.length === 0) {
    $q.notify({
      type: 'warning',
      message: 'Selecciona al menos una figura o una idea'
    })
    return
  }
  
  showMezclaModal.value = true
}

const onPromptGenerated = (promptData) => {
  $q.notify({
    type: 'positive',
    message: 'Prompt generado exitosamente',
    icon: 'auto_awesome'
  })
  
  showMezclaModal.value = false
  
  // Opcional: redirigir a la página de mezcla
  router.push('/mezcla')
}

// Lifecycle
onMounted(async () => {
  await initializeData()
})
</script>

<style scoped>
.modern-explore-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.body--dark .modern-explore-page {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Hero Section */
.hero-section {
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%2310b981" stop-opacity="0.1"/><stop offset="100%" stop-color="%23059669" stop-opacity="0"/></radialGradient></defs><circle cx="150" cy="150" r="100" fill="url(%23a)"/><circle cx="850" cy="200" r="120" fill="url(%23a)"/><circle cx="500" cy="800" r="80" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.hero-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.explore-icon {
  font-size: 3rem;
  animation: rotate 3s linear infinite;
}

.hero-title h1 {
  font-size: 3rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -2px;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Selection Summary */
.selection-summary {
  max-width: 600px;
  margin: 2rem auto 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius-lg);
}

.body--dark .selection-summary {
  background: rgba(255, 255, 255, 0.1);
}

.summary-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-text {
  font-weight: 600;
  color: #1e293b;
}

.body--dark .summary-text {
  color: #f1f5f9;
}

/* Tabs Section */
.tabs-section {
  padding: 2rem 0;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.body--dark .tabs-section {
  background: rgba(0, 0, 0, 0.2);
}

.modern-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: white;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  min-width: 200px;
  justify-content: center;
  position: relative;
}

.body--dark .tab-item {
  background: rgba(255, 255, 255, 0.05);
}

.tab-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

.tab-item.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-medium);
}

.tab-item.active:nth-child(2) {
  background: var(--secondary-gradient);
}

.tab-item span {
  font-weight: 600;
  font-size: 1rem;
}

.tab-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
}

/* Content Section */
.content-section {
  padding: 2rem 0 4rem;
}

.content-panel {
  animation: fadeIn 0.5s ease-out;
}

/* Filters Section */
.filters-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.body--dark .filters-section {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
  margin-bottom: 2rem;
}

.modern-search {
  max-width: 500px;
  margin: 0 auto;
}

.modern-search .q-field__control {
  border-radius: var(--border-radius);
  background: white;
}

.body--dark .modern-search .q-field__control {
  background: rgba(255, 255, 255, 0.05);
}

.category-filters h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  text-align: center;
}

.body--dark .category-filters h4 {
  color: #f1f5f9;
}

.filter-chips {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-chip {
  transition: var(--transition);
  font-weight: 500;
}

.filter-chip:hover {
  transform: scale(1.05);
}

/* Results Info */
.results-info {
  text-align: center;
  margin-bottom: 2rem;
}

.results-count {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* Items Grid */
.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title h1 {
    font-size: 2.5rem;
  }

  .explore-icon {
    font-size: 2.5rem;
  }

  .modern-tabs {
    flex-direction: column;
    align-items: center;
  }

  .tab-item {
    min-width: 250px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
  }

  .filters-section {
    padding: 1.5rem;
  }

  .filter-chips {
    justify-content: center;
  }

  .items-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .items-grid {
    grid-template-columns: 1fr;
  }

  .hero-title {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Loading states */
.loading-state {
  padding: 4rem 2rem;
  color: #64748b;
}

.loading-state .q-spinner-dots {
  margin-bottom: 1rem;
}

/* Local prompt badge */
.local-prompt {
  border-left: 4px solid #f59e0b;
}

.local-badge {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
</style>

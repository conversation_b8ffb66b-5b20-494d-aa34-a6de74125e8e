<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="col-12 col-md-10 col-lg-8">
        <!-- Hero Section -->
        <div class="text-center q-mb-xl">
          <div class="text-h2 q-mb-md">
            🧠 <span class="text-primary">Sabiedumbre</span>
          </div>
          <p class="text-h5 text-grey-7 q-mb-lg">
            Genera textos creativos mezclando figuras del pensamiento con conceptos profundos
          </p>
          <q-btn
            size="lg"
            color="primary"
            icon="auto_awesome"
            label="Comenzar a Crear"
            to="/mezcla"
            class="q-px-xl"
          />
        </div>

        <!-- Features -->
        <div class="row q-gutter-lg q-mb-xl">
          <div class="col-12 col-md-4">
            <q-card class="text-center full-height">
              <q-card-section>
                <q-icon name="people" size="3rem" color="primary" />
                <div class="text-h6 q-mt-md q-mb-sm">Figuras Históricas</div>
                <p class="text-body2">
                  Combina perspectivas de filósofos, artistas, científicos y pensadores de todas las épocas
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-4">
            <q-card class="text-center full-height">
              <q-card-section>
                <q-icon name="lightbulb" size="3rem" color="secondary" />
                <div class="text-h6 q-mt-md q-mb-sm">Conceptos Profundos</div>
                <p class="text-body2">
                  Explora ideas fundamentales: libertad, belleza, tiempo, identidad, amor, muerte...
                </p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-4">
            <q-card class="text-center full-height">
              <q-card-section>
                <q-icon name="auto_awesome" size="3rem" color="accent" />
                <div class="text-h6 q-mt-md q-mb-sm">Textos Únicos</div>
                <p class="text-body2">
                  Genera ensayos, diálogos, narrativas y poemas con conexiones inesperadas
                </p>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- How it works -->
        <div class="q-mb-xl">
          <h4 class="text-h4 text-center q-mb-lg">¿Cómo funciona?</h4>
          <div class="row q-gutter-md">
            <div class="col-12 col-md-3 text-center">
              <q-icon name="filter_1" size="2rem" color="primary" />
              <div class="text-subtitle1 q-mt-sm">Selecciona</div>
              <p class="text-body2">Elige hasta 2 figuras y 2 conceptos</p>
            </div>
            <div class="col-12 col-md-3 text-center">
              <q-icon name="filter_2" size="2rem" color="primary" />
              <div class="text-subtitle1 q-mt-sm">Combina</div>
              <p class="text-body2">El sistema mezcla las perspectivas</p>
            </div>
            <div class="col-12 col-md-3 text-center">
              <q-icon name="filter_3" size="2rem" color="primary" />
              <div class="text-subtitle1 q-mt-sm">Genera</div>
              <p class="text-body2">Obtén un prompt creativo único</p>
            </div>
            <div class="col-12 col-md-3 text-center">
              <q-icon name="filter_4" size="2rem" color="primary" />
              <div class="text-subtitle1 q-mt-sm">Crea</div>
              <p class="text-body2">Usa el prompt para escribir</p>
            </div>
          </div>
        </div>

        <!-- Examples -->
        <div class="q-mb-xl">
          <h4 class="text-h4 text-center q-mb-lg">Ejemplos de Mezclas</h4>
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-card>
                <q-card-section>
                  <div class="text-subtitle1 q-mb-sm">
                    <q-chip color="primary" text-color="white" label="Nietzsche" size="sm" />
                    <q-chip color="primary" text-color="white" label="Frida Kahlo" size="sm" />
                    ×
                    <q-chip color="secondary" text-color="white" label="dolor" size="sm" />
                  </div>
                  <p class="text-body2 text-italic">
                    "Escribe un ensayo que combine las perspectivas de Nietzsche y Frida Kahlo
                    sobre el concepto del dolor como transformación creativa..."
                  </p>
                </q-card-section>
              </q-card>
            </div>
            <div class="col-12 col-md-6">
              <q-card>
                <q-card-section>
                  <div class="text-subtitle1 q-mb-sm">
                    <q-chip color="primary" text-color="white" label="Einstein" size="sm" />
                    ×
                    <q-chip color="secondary" text-color="white" label="tiempo" size="sm" />
                    <q-chip color="secondary" text-color="white" label="belleza" size="sm" />
                  </div>
                  <p class="text-body2 text-italic">
                    "Crea un diálogo imaginario donde Einstein reflexiona sobre
                    la relación entre el tiempo y la belleza en el universo..."
                  </p>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
          <div class="q-gutter-md">
            <q-btn
              size="xl"
              color="primary"
              icon="auto_awesome"
              label="Crear Tu Primera Mezcla"
              to="/mezcla"
              class="q-px-xl q-py-md"
            />
            <br />
            <q-btn
              size="lg"
              color="secondary"
              icon="explore"
              label="Explorar Contenido"
              to="/explorar"
              outline
              class="q-px-lg q-py-sm q-mt-md"
            />
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
defineOptions({
  name: 'IndexPage'
});
</script>

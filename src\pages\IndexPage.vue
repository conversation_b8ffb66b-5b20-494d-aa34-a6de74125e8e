<template>
  <q-page class="modern-page">
    <!-- Hero Section con gradiente -->
    <section class="hero-section">
      <div class="container-modern">
        <div class="hero-content fade-in">
          <div class="hero-title">
            <span class="brain-icon">🧠</span>
            <h1 class="text-gradient">Sabiedumbre</h1>
          </div>
          <p class="hero-subtitle">
            Genera textos creativos mezclando figuras del pensamiento con conceptos profundos
          </p>
          <div class="hero-actions">
            <q-btn
              size="xl"
              class="btn-modern btn-gradient"
              icon="auto_awesome"
              label="Comenzar a Crear"
              to="/mezcla"
              no-caps
            />
            <q-btn
              size="lg"
              class="btn-modern q-ml-md"
              icon="explore"
              label="Explorar"
              to="/explorar"
              outline
              color="primary"
              no-caps
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section section-padding">
      <div class="container-modern">
        <div class="features-grid slide-up">
          <div class="feature-card modern-card hover-lift">
            <div class="feature-icon">
              <q-icon name="people" size="3rem" color="primary" />
            </div>
            <h3>Figuras Históricas</h3>
            <p>Combina perspectivas de filósofos, artistas, científicos y pensadores de todas las épocas</p>
          </div>
          <div class="feature-card modern-card hover-lift">
            <div class="feature-icon">
              <q-icon name="lightbulb" size="3rem" color="secondary" />
            </div>
            <h3>Conceptos Profundos</h3>
            <p>Explora ideas fundamentales: libertad, belleza, tiempo, identidad, amor, muerte...</p>
          </div>
          <div class="feature-card modern-card hover-lift">
            <div class="feature-icon">
              <q-icon name="auto_awesome" size="3rem" color="accent" />
            </div>
            <h3>Textos Únicos</h3>
            <p>Genera ensayos, diálogos, narrativas y poemas con conexiones inesperadas</p>
          </div>
        </div>
      </div>
    </section>

    <!-- How it works -->
    <section class="how-it-works-section section-padding">
      <div class="container-modern">
        <h2 class="section-title text-center">¿Cómo funciona?</h2>
        <div class="steps-grid">
          <div class="step-card glass-card">
            <div class="step-number">1</div>
            <q-icon name="touch_app" size="2.5rem" color="primary" />
            <h4>Selecciona</h4>
            <p>Elige hasta 2 figuras y 2 conceptos</p>
          </div>
          <div class="step-card glass-card">
            <div class="step-number">2</div>
            <q-icon name="auto_fix_high" size="2.5rem" color="primary" />
            <h4>Combina</h4>
            <p>El sistema mezcla las perspectivas</p>
          </div>
          <div class="step-card glass-card">
            <div class="step-number">3</div>
            <q-icon name="psychology" size="2.5rem" color="primary" />
            <h4>Genera</h4>
            <p>Obtén un prompt creativo único</p>
          </div>
          <div class="step-card glass-card">
            <div class="step-number">4</div>
            <q-icon name="create" size="2.5rem" color="primary" />
            <h4>Crea</h4>
            <p>Usa el prompt para escribir</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Examples -->
    <section class="examples-section section-padding">
      <div class="container-modern">
        <h2 class="section-title text-center">Ejemplos de Mezclas</h2>
        <div class="examples-grid">
          <div class="example-card modern-card hover-lift">
            <div class="example-tags">
              <q-chip color="primary" text-color="white" label="Nietzsche" size="sm" />
              <q-chip color="primary" text-color="white" label="Frida Kahlo" size="sm" />
              <span class="multiply-symbol">×</span>
              <q-chip color="secondary" text-color="white" label="dolor" size="sm" />
            </div>
            <p class="example-text">
              "Escribe un ensayo que combine las perspectivas de Nietzsche y Frida Kahlo
              sobre el concepto del dolor como transformación creativa..."
            </p>
          </div>
          <div class="example-card modern-card hover-lift">
            <div class="example-tags">
              <q-chip color="primary" text-color="white" label="Einstein" size="sm" />
              <span class="multiply-symbol">×</span>
              <q-chip color="secondary" text-color="white" label="tiempo" size="sm" />
              <q-chip color="secondary" text-color="white" label="belleza" size="sm" />
            </div>
            <p class="example-text">
              "Crea un diálogo imaginario donde Einstein reflexiona sobre
              la relación entre el tiempo y la belleza en el universo..."
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA -->
    <section class="cta-section">
      <div class="container-modern">
        <div class="cta-content glass-card-strong">
          <h2 class="text-gradient">¿Listo para crear?</h2>
          <p>Comienza tu viaje creativo ahora mismo</p>
          <q-btn
            size="xl"
            class="btn-modern btn-gradient"
            icon="auto_awesome"
            label="Crear Tu Primera Mezcla"
            to="/mezcla"
            no-caps
          />
        </div>
      </div>
    </section>
  </q-page>
</template>

<script setup>
defineOptions({
  name: 'IndexPage'
});
</script>

<style scoped>
.modern-page {
  min-height: 100vh;
  background: var(--bg-gradient-light);
}

.body--dark .modern-page {
  background: var(--bg-gradient-dark);
}

/* Hero Section */
.hero-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="200" fill="url(%23a)"/><circle cx="400" cy="700" r="180" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.hero-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.brain-icon {
  font-size: 4rem;
  animation: pulse 2s infinite;
}

.hero-title h1 {
  font-size: 4.5rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -2px;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #64748b;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Features Section */
.features-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.body--dark .features-section {
  background: rgba(0, 0, 0, 0.2);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: var(--border-radius-lg);
}

.body--dark .feature-card {
  background: rgba(255, 255, 255, 0.05);
}

.feature-icon {
  margin-bottom: 1.5rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.body--dark .feature-card h3 {
  color: #f1f5f9;
}

.feature-card p {
  color: #64748b;
  line-height: 1.6;
}

/* How it works */
.how-it-works-section {
  background: transparent;
}

.section-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 3rem;
  color: #1e293b;
}

.body--dark .section-title {
  color: #f1f5f9;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.step-card {
  text-align: center;
  padding: 2.5rem 1.5rem;
  position: relative;
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: var(--primary-gradient);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
}

.step-card h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 1rem 0;
  color: #1e293b;
}

.body--dark .step-card h4 {
  color: #f1f5f9;
}

/* Examples */
.examples-section {
  background: rgba(255, 255, 255, 0.5);
}

.body--dark .examples-section {
  background: rgba(0, 0, 0, 0.1);
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.example-card {
  padding: 2rem;
  background: white;
}

.body--dark .example-card {
  background: rgba(255, 255, 255, 0.05);
}

.example-tags {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.multiply-symbol {
  font-size: 1.2rem;
  font-weight: 700;
  color: #64748b;
}

.example-text {
  font-style: italic;
  color: #64748b;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
}

.cta-content {
  text-align: center;
  padding: 4rem 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 2rem;
}

/* Animations */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title h1 {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .examples-grid {
    grid-template-columns: 1fr;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>

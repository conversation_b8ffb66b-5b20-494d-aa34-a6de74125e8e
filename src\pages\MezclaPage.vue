<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="col-12 col-md-10 col-lg-8">
        <!-- Header -->
        <div class="text-center q-mb-xl">
          <h3 class="text-h3 q-mb-md">🧠 Crear Mezcla Creativa</h3>
          <p class="text-subtitle1 text-grey-7">
            Combina figuras del pensamiento con conceptos profundos para generar textos creativos únicos
          </p>
        </div>

        <!-- Quick Stats -->
        <div class="row q-gutter-md q-mb-xl">
          <div class="col">
            <q-card class="text-center">
              <q-card-section>
                <q-icon name="people" size="2rem" color="primary" />
                <div class="text-h6 q-mt-sm">{{ figures.length }}</div>
                <div class="text-caption">Figuras Disponibles</div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col">
            <q-card class="text-center">
              <q-card-section>
                <q-icon name="lightbulb" size="2rem" color="secondary" />
                <div class="text-h6 q-mt-sm">{{ ideas.length }}</div>
                <div class="text-caption">Ideas Disponibles</div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col">
            <q-card class="text-center">
              <q-card-section>
                <q-icon name="auto_awesome" size="2rem" color="accent" />
                <div class="text-h6 q-mt-sm">∞</div>
                <div class="text-caption">Combinaciones Posibles</div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Main Action Button -->
        <div class="text-center q-mb-xl">
          <q-btn
            size="xl"
            color="primary"
            icon="auto_awesome"
            label="Crear Nueva Mezcla"
            @click="openMezclaModal"
            class="q-px-xl q-py-md"
          />
        </div>

        <!-- Recent Prompts -->
        <div v-if="recentPrompts.length > 0" class="q-mb-xl">
          <h5 class="text-h5 q-mb-md">📝 Prompts Recientes</h5>
          <div class="row q-gutter-md">
            <div
              v-for="(prompt, index) in recentPrompts"
              :key="index"
              class="col-12 col-md-6"
            >
              <q-card class="cursor-pointer" @click="viewPrompt(prompt)">
                <q-card-section>
                  <div class="text-subtitle2 q-mb-sm">
                    {{ formatPromptTitle(prompt) }}
                  </div>
                  <div class="text-body2 text-grey-7 ellipsis-2-lines">
                    {{ prompt.prompt }}
                  </div>
                  <div class="q-mt-sm">
                    <q-chip
                      v-for="figure in prompt.figures"
                      :key="figure.id"
                      size="sm"
                      color="primary"
                      text-color="white"
                      :label="figure.name"
                    />
                    <q-chip
                      v-for="idea in prompt.ideas"
                      :key="idea.id"
                      size="sm"
                      color="secondary"
                      text-color="white"
                      :label="idea.name"
                    />
                  </div>
                </q-card-section>
                <q-card-actions align="right">
                  <q-btn
                    flat
                    size="sm"
                    icon="content_copy"
                    @click.stop="copyPrompt(prompt.prompt)"
                  />
                  <q-btn
                    flat
                    size="sm"
                    icon="delete"
                    @click.stop="deletePrompt(index)"
                  />
                </q-card-actions>
              </q-card>
            </div>
          </div>
        </div>

        <!-- Categories Preview -->
        <div class="row q-gutter-md">
          <div class="col-12 col-md-6">
            <q-card>
              <q-card-section>
                <div class="text-h6 q-mb-md">
                  <q-icon name="people" class="q-mr-sm" />
                  Figuras por Categoría
                </div>
                <div v-for="category in figureCategories" :key="category.name" class="q-mb-sm">
                  <q-chip
                    :label="`${category.name} (${category.count})`"
                    color="primary"
                    text-color="white"
                    size="sm"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card>
              <q-card-section>
                <div class="text-h6 q-mb-md">
                  <q-icon name="lightbulb" class="q-mr-sm" />
                  Ideas por Dominio
                </div>
                <div v-for="domain in ideaDomains" :key="domain.name" class="q-mb-sm">
                  <q-chip
                    :label="`${domain.name} (${domain.count})`"
                    color="secondary"
                    text-color="white"
                    size="sm"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Mezcla -->
    <ModalMezcla
      v-model="showMezclaModal"
      :figures="figures"
      :ideas="ideas"
      @prompt-generated="onPromptGenerated"
    />

    <!-- Modal de Vista de Prompt -->
    <q-dialog v-model="showPromptModal">
      <q-card style="min-width: 500px;">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">📝 Prompt Generado</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section v-if="selectedPrompt">
          <div class="text-body1 q-mb-md">{{ selectedPrompt.prompt }}</div>
          <div class="q-mt-md">
            <q-chip
              v-for="figure in selectedPrompt.figures"
              :key="figure.id"
              color="primary"
              text-color="white"
              :label="figure.name"
            />
            <q-chip
              v-for="idea in selectedPrompt.ideas"
              :key="idea.id"
              color="secondary"
              text-color="white"
              :label="idea.name"
            />
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn
            color="primary"
            icon="content_copy"
            label="Copiar"
            @click="copyPrompt(selectedPrompt?.prompt)"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import ModalMezcla from '../components/ModalMezcla.vue'
import figuresData from '../data/figures.json'
import ideasData from '../data/ideas.json'

// Composables
const $q = useQuasar()

// Reactive data
const figures = ref([])
const ideas = ref([])
const showMezclaModal = ref(false)
const showPromptModal = ref(false)
const selectedPrompt = ref(null)
const recentPrompts = ref([])

// Computed
const figureCategories = computed(() => {
  const categories = {}
  figures.value.forEach(figure => {
    if (!categories[figure.category]) {
      categories[figure.category] = { name: figure.category, count: 0 }
    }
    categories[figure.category].count++
  })
  return Object.values(categories)
})

const ideaDomains = computed(() => {
  const domains = {}
  ideas.value.forEach(idea => {
    if (!domains[idea.domain]) {
      domains[idea.domain] = { name: idea.domain, count: 0 }
    }
    domains[idea.domain].count++
  })
  return Object.values(domains)
})

// Methods
const loadData = async () => {
  try {
    // Cargar figuras e ideas desde imports
    figures.value = figuresData
    ideas.value = ideasData

    // Cargar prompts recientes del localStorage
    const savedPrompts = localStorage.getItem('sabiedumbre-recent-prompts')
    if (savedPrompts) {
      recentPrompts.value = JSON.parse(savedPrompts)
    }
  } catch (error) {
    console.error('Error loading data:', error)
    $q.notify({
      type: 'negative',
      message: 'Error al cargar los datos'
    })
  }
}

const openMezclaModal = () => {
  showMezclaModal.value = true
}

const onPromptGenerated = (promptData) => {
  // Agregar timestamp
  const promptWithTimestamp = {
    ...promptData,
    timestamp: new Date().toISOString()
  }
  
  // Agregar a prompts recientes (máximo 10)
  recentPrompts.value.unshift(promptWithTimestamp)
  if (recentPrompts.value.length > 10) {
    recentPrompts.value = recentPrompts.value.slice(0, 10)
  }
  
  // Guardar en localStorage
  localStorage.setItem('sabiedumbre-recent-prompts', JSON.stringify(recentPrompts.value))
  
  // Cerrar modal
  showMezclaModal.value = false
  
  // Mostrar notificación
  $q.notify({
    type: 'positive',
    message: 'Prompt generado exitosamente',
    icon: 'auto_awesome'
  })
}

const formatPromptTitle = (prompt) => {
  const figureNames = prompt.figures.map(f => f.name).join(' & ')
  const ideaNames = prompt.ideas.map(i => i.name).join(' & ')
  
  if (figureNames && ideaNames) {
    return `${figureNames} × ${ideaNames}`
  } else if (figureNames) {
    return figureNames
  } else if (ideaNames) {
    return ideaNames
  }
  return 'Prompt sin título'
}

const viewPrompt = (prompt) => {
  selectedPrompt.value = prompt
  showPromptModal.value = true
}

const copyPrompt = async (promptText) => {
  try {
    await navigator.clipboard.writeText(promptText)
    $q.notify({
      type: 'positive',
      message: 'Prompt copiado al portapapeles'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Error al copiar el prompt'
    })
  }
}

const deletePrompt = (index) => {
  $q.dialog({
    title: 'Confirmar eliminación',
    message: '¿Estás seguro de que quieres eliminar este prompt?',
    cancel: true,
    persistent: true
  }).onOk(() => {
    recentPrompts.value.splice(index, 1)
    localStorage.setItem('sabiedumbre-recent-prompts', JSON.stringify(recentPrompts.value))
    $q.notify({
      type: 'positive',
      message: 'Prompt eliminado'
    })
  })
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

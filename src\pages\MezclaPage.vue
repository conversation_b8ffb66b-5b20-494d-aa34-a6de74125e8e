<template>
  <q-page class="modern-mezcla-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container-modern">
        <div class="hero-content fade-in">
          <div class="hero-title">
            <span class="brain-icon">🧠</span>
            <h1 class="text-gradient">Crear Mezcla Creativa</h1>
          </div>
          <p class="hero-subtitle">
            Combina figuras del pensamiento con conceptos profundos para generar textos creativos únicos
          </p>

          <!-- Quick Stats -->
          <div class="stats-grid">
            <div class="stat-card glass-card hover-lift">
              <div class="stat-icon">
                <q-icon name="people" size="2.5rem" color="primary" />
              </div>
              <div class="stat-number">{{ figures.length }}</div>
              <div class="stat-label">Figuras Disponibles</div>
            </div>
            <div class="stat-card glass-card hover-lift">
              <div class="stat-icon">
                <q-icon name="lightbulb" size="2.5rem" color="secondary" />
              </div>
              <div class="stat-number">{{ ideas.length }}</div>
              <div class="stat-label">Ideas Disponibles</div>
            </div>
            <div class="stat-card glass-card hover-lift">
              <div class="stat-icon">
                <q-icon name="auto_awesome" size="2.5rem" color="accent" />
              </div>
              <div class="stat-number">∞</div>
              <div class="stat-label">Combinaciones Posibles</div>
            </div>
          </div>

          <!-- Main Action -->
          <div class="hero-action">
            <q-btn
              size="xl"
              class="btn-modern btn-gradient main-action-btn"
              icon="auto_awesome"
              label="Crear Nueva Mezcla"
              @click="openMezclaModal"
              no-caps
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Recent Prompts Section -->
    <section v-if="recentPrompts.length > 0" class="recent-prompts-section section-padding">
      <div class="container-modern">
        <h2 class="section-title text-center">📝 Prompts Recientes</h2>
        <div class="prompts-grid">
          <div
            v-for="(prompt, index) in recentPrompts"
            :key="index"
            class="prompt-card modern-card hover-lift"
            @click="viewPrompt(prompt)"
          >
            <div class="prompt-header">
              <h4 class="prompt-title">{{ formatPromptTitle(prompt) }}</h4>
              <div class="prompt-date">{{ formatDate(prompt.timestamp) }}</div>
            </div>

            <div class="prompt-preview">
              {{ prompt.prompt }}
            </div>

            <div class="prompt-tags">
              <q-chip
                v-for="figure in prompt.figures"
                :key="figure.id"
                size="sm"
                color="primary"
                text-color="white"
                :label="figure.name"
              />
              <q-chip
                v-for="idea in prompt.ideas"
                :key="idea.id"
                size="sm"
                color="secondary"
                text-color="white"
                :label="idea.name"
              />
            </div>

            <div class="prompt-actions">
              <q-btn
                flat
                round
                icon="content_copy"
                @click.stop="copyPrompt(prompt.prompt)"
                class="action-btn"
                title="Copiar prompt"
              />
              <q-btn
                flat
                round
                icon="delete"
                @click.stop="deletePrompt(index)"
                class="action-btn delete-btn"
                title="Eliminar prompt"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories Preview -->
    <section class="categories-section section-padding">
      <div class="container-modern">
        <h2 class="section-title text-center">🎯 Explora por Categorías</h2>
        <div class="categories-grid">
          <div class="category-card glass-card hover-lift">
            <div class="category-header">
              <q-icon name="people" size="2rem" color="primary" />
              <h3>Figuras por Categoría</h3>
            </div>
            <div class="category-content">
              <div v-for="category in figureCategories" :key="category.name" class="category-item">
                <q-chip
                  :label="`${category.name} (${category.count})`"
                  color="primary"
                  text-color="white"
                  size="md"
                  class="category-chip"
                />
              </div>
            </div>
          </div>

          <div class="category-card glass-card hover-lift">
            <div class="category-header">
              <q-icon name="lightbulb" size="2rem" color="secondary" />
              <h3>Ideas por Dominio</h3>
            </div>
            <div class="category-content">
              <div v-for="domain in ideaDomains" :key="domain.name" class="category-item">
                <q-chip
                  :label="`${domain.name} (${domain.count})`"
                  color="secondary"
                  text-color="white"
                  size="md"
                  class="category-chip"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Explore Button -->
        <div class="explore-action text-center">
          <q-btn
            size="lg"
            class="btn-modern"
            color="secondary"
            icon="explore"
            label="Explorar Todo el Contenido"
            to="/explorar"
            outline
            no-caps
          />
        </div>
      </div>
    </section>

    <!-- Modal de Mezcla -->
    <ModalMezcla
      v-model="showMezclaModal"
      :figures="figures"
      :ideas="ideas"
      @prompt-generated="onPromptGenerated"
    />

    <!-- Modal de Vista de Prompt -->
    <q-dialog v-model="showPromptModal">
      <q-card style="min-width: 500px;">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">📝 Prompt Generado</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section v-if="selectedPrompt">
          <div class="text-body1 q-mb-md">{{ selectedPrompt.prompt }}</div>
          <div class="q-mt-md">
            <q-chip
              v-for="figure in selectedPrompt.figures"
              :key="figure.id"
              color="primary"
              text-color="white"
              :label="figure.name"
            />
            <q-chip
              v-for="idea in selectedPrompt.ideas"
              :key="idea.id"
              color="secondary"
              text-color="white"
              :label="idea.name"
            />
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn
            color="primary"
            icon="content_copy"
            label="Copiar"
            @click="copyPrompt(selectedPrompt?.prompt)"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import ModalMezcla from '../components/ModalMezcla.vue'
import figuresData from '../data/figures.json'
import ideasData from '../data/ideas.json'

// Composables
const $q = useQuasar()

// Reactive data
const figures = ref([])
const ideas = ref([])
const showMezclaModal = ref(false)
const showPromptModal = ref(false)
const selectedPrompt = ref(null)
const recentPrompts = ref([])

// Computed
const figureCategories = computed(() => {
  const categories = {}
  figures.value.forEach(figure => {
    if (!categories[figure.category]) {
      categories[figure.category] = { name: figure.category, count: 0 }
    }
    categories[figure.category].count++
  })
  return Object.values(categories)
})

const ideaDomains = computed(() => {
  const domains = {}
  ideas.value.forEach(idea => {
    if (!domains[idea.domain]) {
      domains[idea.domain] = { name: idea.domain, count: 0 }
    }
    domains[idea.domain].count++
  })
  return Object.values(domains)
})

// Methods
const loadData = async () => {
  try {
    // Cargar figuras e ideas desde imports
    figures.value = figuresData
    ideas.value = ideasData

    // Cargar prompts recientes del localStorage
    const savedPrompts = localStorage.getItem('sabiedumbre-recent-prompts')
    if (savedPrompts) {
      recentPrompts.value = JSON.parse(savedPrompts)
    }
  } catch (error) {
    console.error('Error loading data:', error)
    $q.notify({
      type: 'negative',
      message: 'Error al cargar los datos'
    })
  }
}

const openMezclaModal = () => {
  showMezclaModal.value = true
}

const onPromptGenerated = (promptData) => {
  console.log('Prompt generated:', promptData) // Debug

  // Agregar a prompts recientes (máximo 10)
  recentPrompts.value.unshift(promptData)
  if (recentPrompts.value.length > 10) {
    recentPrompts.value = recentPrompts.value.slice(0, 10)
  }

  // Guardar en localStorage
  try {
    localStorage.setItem('sabiedumbre-recent-prompts', JSON.stringify(recentPrompts.value))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }

  // Cerrar modal
  showMezclaModal.value = false

  // Usar alert nativo en lugar de $q.notify
  alert('Prompt generado y guardado exitosamente')
}

const formatPromptTitle = (prompt) => {
  const figureNames = prompt.figures.map(f => f.name).join(' & ')
  const ideaNames = prompt.ideas.map(i => i.name).join(' & ')

  if (figureNames && ideaNames) {
    return `${figureNames} × ${ideaNames}`
  } else if (figureNames) {
    return figureNames
  } else if (ideaNames) {
    return ideaNames
  }
  return 'Prompt sin título'
}

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    return 'Hace unos minutos'
  } else if (diffInHours < 24) {
    return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`
  }
}

const viewPrompt = (prompt) => {
  selectedPrompt.value = prompt
  showPromptModal.value = true
}

const copyPrompt = async (promptText) => {
  try {
    await navigator.clipboard.writeText(promptText)
    alert('Prompt copiado al portapapeles')
  } catch (error) {
    alert('Error al copiar el prompt')
  }
}

const deletePrompt = (index) => {
  if (confirm('¿Estás seguro de que quieres eliminar este prompt?')) {
    recentPrompts.value.splice(index, 1)
    localStorage.setItem('sabiedumbre-recent-prompts', JSON.stringify(recentPrompts.value))
    alert('Prompt eliminado')
  }
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.modern-mezcla-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.body--dark .modern-mezcla-page {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Hero Section */
.hero-section {
  min-height: 70vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0"/></radialGradient></defs><circle cx="300" cy="200" r="120" fill="url(%23a)"/><circle cx="700" cy="400" r="150" fill="url(%23a)"/><circle cx="200" cy="600" r="100" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.hero-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.brain-icon {
  font-size: 3.5rem;
  animation: pulse 2s infinite;
}

.hero-title h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -2px;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #64748b;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  text-align: center;
  padding: 2rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-lg);
  transition: var(--transition);
}

.body--dark .stat-card {
  background: rgba(255, 255, 255, 0.1);
}

.stat-icon {
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.body--dark .stat-number {
  color: #f1f5f9;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* Hero Action */
.hero-action {
  margin-top: 2rem;
}

.main-action-btn {
  padding: 1rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: var(--shadow-medium);
}

.main-action-btn:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

/* Recent Prompts Section */
.recent-prompts-section {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.body--dark .recent-prompts-section {
  background: rgba(0, 0, 0, 0.2);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 3rem;
  color: #1e293b;
}

.body--dark .section-title {
  color: #f1f5f9;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.prompt-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.body--dark .prompt-card {
  background: rgba(255, 255, 255, 0.05);
}

.prompt-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.prompt-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.body--dark .prompt-title {
  color: #f1f5f9;
}

.prompt-date {
  font-size: 0.8rem;
  color: #64748b;
  font-style: italic;
}

.prompt-preview {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.prompt-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-btn {
  color: #64748b;
  transition: var(--transition);
}

.action-btn:hover {
  color: #3b82f6;
  transform: scale(1.1);
}

.delete-btn:hover {
  color: #ef4444;
}

/* Categories Section */
.categories-section {
  background: transparent;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.category-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  transition: var(--transition);
}

.body--dark .category-card {
  background: rgba(255, 255, 255, 0.1);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.category-header h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.body--dark .category-header h3 {
  color: #f1f5f9;
}

.category-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.category-item {
  margin-bottom: 0.5rem;
}

.category-chip {
  font-weight: 500;
  transition: var(--transition);
}

.category-chip:hover {
  transform: scale(1.05);
}

.explore-action {
  margin-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title h1 {
    font-size: 2.5rem;
  }

  .brain-icon {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .prompts-grid {
    grid-template-columns: 1fr;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 2rem;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
</style>


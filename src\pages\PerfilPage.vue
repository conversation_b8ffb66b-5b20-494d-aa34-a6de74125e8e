<template>
  <q-page class="modern-profile-page">
    <!-- Hero Section -->
    <section class="profile-hero">
      <div class="container-modern">
        <div class="hero-content fade-in">
          <div class="profile-avatar">
            <div class="avatar-container glass-card">
              <q-icon name="person" size="3rem" />
            </div>
          </div>
          <h1 class="profile-name text-gradient">Mi Perfil</h1>
          <p class="profile-subtitle">Gestiona tu cuenta y preferencias de Sabiedumbre</p>
        </div>
      </div>
    </section>

    <!-- Profile Content -->
    <section class="profile-content section-padding">
      <div class="container-modern">
        <div class="profile-grid">
          <!-- Información Personal -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="person_outline" size="1.5rem" color="primary" />
              <h3>Información Personal</h3>
            </div>
            <div class="card-content">
              <div class="info-item">
                <label>Nombre de usuario</label>
                <div class="info-value">{{ userInfo.username }}</div>
              </div>
              <div class="info-item">
                <label>Email</label>
                <div class="info-value">{{ userInfo.email }}</div>
              </div>
              <div class="info-item">
                <label>Miembro desde</label>
                <div class="info-value">{{ formatDate(userInfo.joinDate) }}</div>
              </div>
            </div>
            <div class="card-actions">
              <q-btn
                class="btn-modern btn-minimal"
                icon="edit"
                label="Editar Perfil"
                @click="editProfile"
                no-caps
              />
            </div>
          </div>

          <!-- Estadísticas -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="analytics" size="1.5rem" color="secondary" />
              <h3>Estadísticas</h3>
            </div>
            <div class="card-content">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number text-gradient">{{ stats.totalPrompts }}</div>
                  <div class="stat-label">Prompts Generados</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number text-gradient-secondary">{{ stats.favoriteStyle }}</div>
                  <div class="stat-label">Estilo Favorito</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number text-gradient-accent">{{ stats.daysActive }}</div>
                  <div class="stat-label">Días Activo</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mis Prompts -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="auto_awesome" size="1.5rem" color="accent" />
              <h3>Mis Prompts Recientes</h3>
            </div>
            <div class="card-content">
              <div v-if="recentPrompts.length > 0" class="prompts-list">
                <div
                  v-for="(prompt, index) in recentPrompts.slice(0, 3)"
                  :key="index"
                  class="prompt-item"
                >
                  <div class="prompt-preview">{{ prompt.prompt.substring(0, 80) }}...</div>
                  <div class="prompt-meta">
                    <span class="prompt-date">{{ formatRelativeDate(prompt.timestamp) }}</span>
                    <q-btn
                      flat
                      round
                      icon="content_copy"
                      size="sm"
                      @click="copyPrompt(prompt.prompt)"
                      class="copy-btn"
                    />
                  </div>
                </div>
              </div>
              <div v-else class="empty-prompts">
                <q-icon name="auto_awesome" size="2rem" color="grey-5" />
                <p>Aún no has generado prompts</p>
              </div>
            </div>
            <div class="card-actions">
              <q-btn
                class="btn-modern btn-minimal"
                icon="visibility"
                label="Ver Todos"
                @click="viewAllPrompts"
                no-caps
              />
            </div>
          </div>

          <!-- Configuración -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="settings" size="1.5rem" color="primary" />
              <h3>Configuración</h3>
            </div>
            <div class="card-content">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">Modo Oscuro</div>
                  <div class="setting-description">Cambia la apariencia de la aplicación</div>
                </div>
                <q-toggle
                  v-model="darkMode"
                  @update:model-value="toggleDarkMode"
                  color="primary"
                />
              </div>
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">Notificaciones</div>
                  <div class="setting-description">Recibe notificaciones sobre nuevas funciones</div>
                </div>
                <q-toggle
                  v-model="notifications"
                  color="secondary"
                />
              </div>
            </div>
          </div>

          <!-- Seguridad -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="security" size="1.5rem" color="warning" />
              <h3>Seguridad</h3>
            </div>
            <div class="card-content">
              <div class="security-item">
                <q-icon name="lock" class="security-icon" />
                <div class="security-info">
                  <div class="security-title">Cambiar Contraseña</div>
                  <div class="security-description">Actualiza tu contraseña regularmente</div>
                </div>
                <q-btn
                  class="btn-modern btn-minimal"
                  icon="arrow_forward"
                  @click="changePassword"
                  round
                />
              </div>
              <div class="security-item">
                <q-icon name="download" class="security-icon" />
                <div class="security-info">
                  <div class="security-title">Exportar Datos</div>
                  <div class="security-description">Descarga todos tus prompts y datos</div>
                </div>
                <q-btn
                  class="btn-modern btn-minimal"
                  icon="arrow_forward"
                  @click="exportData"
                  round
                />
              </div>
            </div>
          </div>

          <!-- Soporte -->
          <div class="profile-card glass-card hover-lift">
            <div class="card-header">
              <q-icon name="help_outline" size="1.5rem" color="info" />
              <h3>Ayuda y Soporte</h3>
            </div>
            <div class="card-content">
              <div class="support-links">
                <a href="#" class="support-link">
                  <q-icon name="description" />
                  <span>Documentación</span>
                  <q-icon name="open_in_new" size="0.8rem" />
                </a>
                <a href="#" class="support-link">
                  <q-icon name="contact_support" />
                  <span>Contactar Soporte</span>
                  <q-icon name="open_in_new" size="0.8rem" />
                </a>
                <a href="#" class="support-link">
                  <q-icon name="feedback" />
                  <span>Enviar Feedback</span>
                  <q-icon name="open_in_new" size="0.8rem" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

defineOptions({
  name: 'PerfilPage'
})

// Composables
const $q = useQuasar()

// Reactive data
const userInfo = ref({
  username: 'Usuario Demo',
  email: '<EMAIL>',
  joinDate: '2024-01-15'
})

const stats = ref({
  totalPrompts: 0,
  favoriteStyle: 'Ensayo',
  daysActive: 1
})

const recentPrompts = ref([])
const darkMode = ref(false)
const notifications = ref(true)

// Computed
const formattedStats = computed(() => {
  return {
    ...stats.value,
    totalPrompts: stats.value.totalPrompts || 0
  }
})

// Methods
const loadUserData = () => {
  // Cargar prompts del localStorage
  const savedPrompts = localStorage.getItem('sabiedumbre-recent-prompts')
  if (savedPrompts) {
    recentPrompts.value = JSON.parse(savedPrompts)
    stats.value.totalPrompts = recentPrompts.value.length
  }
  
  // Detectar modo oscuro actual
  darkMode.value = $q.dark.isActive
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatRelativeDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Hace unos minutos'
  } else if (diffInHours < 24) {
    return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`
  }
}

const copyPrompt = async (promptText) => {
  try {
    await navigator.clipboard.writeText(promptText)
    $q.notify({
      type: 'positive',
      message: 'Prompt copiado al portapapeles',
      position: 'top'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Error al copiar el prompt',
      position: 'top'
    })
  }
}

const toggleDarkMode = () => {
  $q.dark.toggle()
}

const editProfile = () => {
  $q.notify({
    type: 'info',
    message: 'Función de edición de perfil próximamente',
    position: 'top'
  })
}

const viewAllPrompts = () => {
  $q.notify({
    type: 'info',
    message: 'Vista completa de prompts próximamente',
    position: 'top'
  })
}

const changePassword = () => {
  $q.notify({
    type: 'info',
    message: 'Cambio de contraseña próximamente',
    position: 'top'
  })
}

const exportData = () => {
  $q.notify({
    type: 'info',
    message: 'Exportación de datos próximamente',
    position: 'top'
  })
}

// Lifecycle
onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.modern-profile-page {
  min-height: 100vh;
  background: var(--bg-gradient-light);
}

.body--dark .modern-profile-page {
  background: var(--bg-gradient-dark);
}

/* Hero Section */
.profile-hero {
  padding: 4rem 0 2rem;
  text-align: center;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.profile-avatar {
  margin-bottom: 2rem;
}

.avatar-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: var(--glass-bg-strong);
  color: var(--text-secondary);
}

.profile-name {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.profile-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Profile Content */
.profile-content {
  padding: 2rem 0 4rem;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Profile Cards */
.profile-card {
  background: var(--glass-bg-strong);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  transition: var(--transition);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
}

.body--dark .card-header h3 {
  color: var(--text-inverse);
}

.card-content {
  margin-bottom: 2rem;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

/* Info Items */
.info-item {
  margin-bottom: 1.5rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-tertiary);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.body--dark .info-value {
  color: var(--text-inverse);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* Prompts List */
.prompts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.prompt-item {
  padding: 1rem;
  background: var(--glass-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--glass-border);
  margin-bottom: 1rem;
}

.prompt-preview {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.prompt-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prompt-date {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.copy-btn {
  color: var(--text-tertiary);
  transition: var(--transition-fast);
}

.copy-btn:hover {
  color: var(--text-primary);
}

.empty-prompts {
  text-align: center;
  padding: 2rem;
  color: var(--text-tertiary);
}

.empty-prompts p {
  margin-top: 1rem;
  font-style: italic;
}

/* Settings */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--glass-border);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.body--dark .setting-title {
  color: var(--text-inverse);
}

.setting-description {
  font-size: 0.9rem;
  color: var(--text-tertiary);
}

/* Security */
.security-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--glass-border);
}

.security-item:last-child {
  border-bottom: none;
}

.security-icon {
  color: var(--text-tertiary);
  min-width: 24px;
}

.security-info {
  flex: 1;
}

.security-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.body--dark .security-title {
  color: var(--text-inverse);
}

.security-description {
  font-size: 0.9rem;
  color: var(--text-tertiary);
}

/* Support Links */
.support-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.support-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--glass-bg);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--text-secondary);
  transition: var(--transition-fast);
  border: 1px solid var(--glass-border);
}

.support-link:hover {
  background: var(--glass-bg-strong);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.support-link span {
  flex: 1;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .profile-name {
    font-size: 2.5rem;
  }

  .profile-card {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .avatar-container {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .setting-item,
  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>

const routes = [
  {
    path: "/",
    component: () => import("layouts/MainLayout.vue"),
    children: [
      { path: "", component: () => import("pages/IndexPage.vue") },
      { path: "/mezcla", component: () => import("pages/MezclaPage.vue") },
      { path: "/explorar", component: () => import("pages/ExplorePage.vue") },
      { path: "/perfil", component: () => import("pages/PerfilPage.vue") },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;

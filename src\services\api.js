import axios from "axios";

// Create axios instance with base configuration
const baseURL =
  import.meta.env.VITE_API_URL ||
  process.env.VUE_APP_API_URL ||
  "http://localhost:3001/api";

console.log("🔗 API Base URL:", baseURL);

const api = axios.create({
  baseURL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available (for future use)
    const token = localStorage.getItem("sabiedumbre-token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error("API Error:", error.response?.data || error.message);
    return Promise.reject(error.response?.data || error);
  }
);

// Figures API
export const figuresAPI = {
  // Get all figures
  getAll: (params = {}) => api.get("/figures", { params }),

  // Get figure by ID
  getById: (id) => api.get(`/figures/${id}`),

  // Get categories
  getCategories: () => api.get("/figures/meta/categories"),

  // Create figure (admin)
  create: (data) => api.post("/figures", data),

  // Update figure (admin)
  update: (id, data) => api.put(`/figures/${id}`, data),

  // Delete figure (admin)
  delete: (id) => api.delete(`/figures/${id}`),
};

// Ideas API
export const ideasAPI = {
  // Get all ideas
  getAll: (params = {}) => api.get("/ideas", { params }),

  // Get idea by ID
  getById: (id) => api.get(`/ideas/${id}`),

  // Get domains
  getDomains: () => api.get("/ideas/meta/domains"),

  // Create idea (admin)
  create: (data) => api.post("/ideas", data),

  // Update idea (admin)
  update: (id, data) => api.put(`/ideas/${id}`, data),

  // Delete idea (admin)
  delete: (id) => api.delete(`/ideas/${id}`),
};

// Prompts API
export const promptsAPI = {
  // Get all prompts for user
  getAll: (params = {}) => api.get("/prompts", { params }),

  // Get prompt by ID
  getById: (id) => api.get(`/prompts/${id}`),

  // Create new prompt
  create: (data) => api.post("/prompts", data),

  // Delete prompt
  delete: (id) => api.delete(`/prompts/${id}`),

  // Get user statistics
  getStats: (userId = "anonymous") => api.get(`/prompts/stats/${userId}`),
};

// Health check
export const healthAPI = {
  check: () => api.get("/health"),
};

export default api;
